<!DOCTYPE html>
<?php
error_reporting(0);
session_start();
include 'config.php';
$uid = $_SESSION['xinhu_adminid'];
$projectid=$_SESSION['xinhu_projectid'];
$project=$_SESSION['xinhu_project'];
?>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>项目首页</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="./css/moble.css" />
  </head>
  <body>
    <div id="app">
      <div class="app-container">
          
      
        <form id="form1" name="form1" method="post" action="qhxm.php?pagename=moble">
          <p>
            <label>
           &nbsp;&nbsp;&nbsp;&nbsp;当前项目： <select name="projectid" style="width: 150px;">
              <?php
                $sql = "SELECT * FROM tuqoa_rydp WHERE dpryxmid=$uid and state='在职' and status=1";
                $result = mysqli_query($link, $sql);
                if(mysqli_num_rows($result) > 0){
                    //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
                    while($row = mysqli_fetch_assoc($result)){
                        $mr="";
                        if($row["drxmid"] == $_SESSION['xinhu_projectid']){
                            $mr="selected";
                        }else{
                            $mr="";
                        }
              ?>  
              <option value="<?php echo $row["drxmid"]?>" <?php echo $mr?>><?php echo $row["drxm"]?></option>
              <?php
                    }
                }else{
                    ?>  
                      <option value="0">无项目</option>
                     <?php
                }
              ?>
            </select>
            </label>
            &nbsp;&nbsp;
            <input type="submit" name="Submit" value="切换项目" /><?php echo($xmmc)?>
          </p>
        </form>
        <div class="workbench">
          <van-cell title="工作台帐" class="workbench-cell">
            <template #value>
             
              <van-icon
                name="arrow-down"
                style="margin-left: 5px"
                color="var(--van-cell-value-color)"
              />
            </template>
          </van-cell>
          <div class="workbench-date">
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums blue">0</div>
              <div class="workbench-date-item-desc">巡视</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums blue">0</div>
              <div class="workbench-date-item-desc">旁站</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums green">0</div>
              <div class="workbench-date-item-desc">进场验收</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums green">0</div>
              <div class="workbench-date-item-desc">工程验收</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums yellow">0</div>
              <div class="workbench-date-item-desc">见证检验</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums blue">0</div>
              <div class="workbench-date-item-desc">平行检验</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums green">0</div>
              <div class="workbench-date-item-desc">会议</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums yellow">0</div>
              <div class="workbench-date-item-desc">收发文</div>
            </div>
          </div>
        </div>
    <!--
        <div class="phase">
          <van-cell title="阶段工作" class="workbench-cell"> </van-cell>

          <div class="tree-select">
            <div class="tree-select-slider">
              <div
                :class="['tree-select-slider-item',item.value === currentActive ? 'active' :'']"
                v-for="item in options"
                @click="currentActive = item.value"
              >
                {{item.name}}
              </div>
            </div>
            
            <div class="tree-select-container">
                
              <div
                class="tree-select-container-pane"
                v-if="currentActive === '工程准备阶段'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>

              <div
                class="tree-select-container-pane"
                v-if="currentActive === '施工阶段'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>

              <div
                class="tree-select-container-pane"
                v-if="currentActive === '竣工验收阶段'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>

              <div
                class="tree-select-container-pane"
                v-if="currentActive === '工程质量'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      -->

      <van-popup
        v-model:show="show"
        :style="{padding:'10px 0', width:'500px' }"
      >
        <van-radio-group v-model="currentTitle">
          <van-cell-group inset>
            <van-cell
              v-for="item in selectOptions"
              :title="item.title"
              clickable
              @click="currentTitle = item.name;show = false"
            >
              <template #right-icon>
                <van-radio :name="item.name" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </van-popup>
    </div>
  </body>

  <script>
    // 在 #app 标签下渲染一个按钮组件
    const app = Vue.createApp({
      setup() {
        const options = Vue.ref([
          {
            name: "工程准备阶段",
            value: "工程准备阶段",
          },
          {
            name: "施工阶段",
            value: "施工阶段",
          },
          {
            name: "竣工验收阶段",
            value: "竣工验收阶段",
          },
          {
            name: "工程质量",
            value: "工程质量",
          },
        ]);

        const selectOptions = [
          {
            name: "绿地地产",
            title: "绿地地产",
          },
          {
            name: "项目经理",
            title: "项目经理",
          },
        ];

        const currentActive = Vue.ref("工程准备阶段");

        const show = Vue.ref(false);

        const currentTitle = Vue.ref("绿地地产");

        return {
          options,
          currentActive,
          show,
          selectOptions,
          currentTitle,
        };
      },
    });

    app.use(vant);

    app.use(vant.Lazyload);

    app.mount("#app");
  </script>
</html>
