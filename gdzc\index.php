<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>条形码扫描器</title>
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            padding: 20px;
            box-sizing: border-box;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .scanner-container {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: 0 auto 20px;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
            background-color: #000;
            aspect-ratio: 4/3;
        }
        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        #canvas {
            display: none;
        }
        .scan-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .scan-box {
            width: 70%;
            height: 30%;
            border: 3px solid rgba(255, 0, 0, 0.5);
            box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 0.7);
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        button#stopButton {
            background-color: #f44336;
        }
        .result-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        #barcodeInput {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        #status {
            text-align: center;
            margin: 10px 0;
            min-height: 20px;
            color: #666;
        }
        .error {
            color: #f44336;
            text-align: center;
            margin: 10px 0;
        }
        .success {
            color: #4CAF50;
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>条形码扫描器</h1>
        
        <div class="scanner-container">
            <video id="video" playsinline></video>
            <canvas id="canvas"></canvas>
            <div class="scan-overlay">
                <div class="scan-box"></div>
            </div>
        </div>
        
        <div class="controls">
            <button id="startButton">开始扫描</button>
            <button id="stopButton" disabled>停止扫描</button>
        </div>
        
        <div id="status">准备就绪</div>
        <div id="error" class="error"></div>
        <div id="success" class="success"></div>
        
        <div class="result-container">
            <label for="barcodeInput">扫描结果：</label>
            <input type="text" id="barcodeInput" readonly placeholder="扫描结果将显示在这里">
        </div>
    </div>

    <script>
        // 获取DOM元素
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const startButton = document.getElementById('startButton');
        const stopButton = document.getElementById('stopButton');
        const barcodeInput = document.getElementById('barcodeInput');
        const statusDiv = document.getElementById('status');
        const errorDiv = document.getElementById('error');
        const successDiv = document.getElementById('success');
        
        let stream = null;
        let scanning = false;
        let animationFrameId = null;
        
        // 更新状态显示
        function updateStatus(message, isError = false, isSuccess = false) {
            statusDiv.textContent = message;
            errorDiv.textContent = isError ? message : '';
            successDiv.textContent = isSuccess ? message : '';
        }
        
        // 开始扫描
        startButton.addEventListener('click', async () => {
            updateStatus('正在启动摄像头...');
            errorDiv.textContent = '';
            successDiv.textContent = '';
            barcodeInput.value = '';
            startButton.disabled = true;
            stopButton.disabled = false;
            
            try {
                // 请求摄像头访问权限
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment', // 使用后置摄像头
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    },
                    audio: false
                });
                
                video.srcObject = stream;
                
                // 等待视频元数据加载完成
                await new Promise((resolve) => {
                    video.onloadedmetadata = () => {
                        video.play();
                        resolve();
                    };
                    video.onerror = () => {
                        throw new Error('视频播放失败');
                    };
                });
                
                scanning = true;
                updateStatus('正在扫描条形码...');
                
                // 开始扫描处理
                scanBarcode();
            } catch (err) {
                updateStatus('摄像头访问失败', true);
                console.error('摄像头错误:', err);
                startButton.disabled = false;
                stopButton.disabled = true;
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                    stream = null;
                }
            }
        });
        
        // 停止扫描
        stopButton.addEventListener('click', () => {
            stopScanning();
            updateStatus('扫描已停止');
        });
        
        function stopScanning() {
            scanning = false;
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
                video.srcObject = null;
            }
            startButton.disabled = false;
            stopButton.disabled = true;
        }
        
        // 扫描条形码
        function scanBarcode() {
            if (!scanning) return;
            
            // 确保视频已经准备好并且有尺寸
            if (video.readyState === video.HAVE_ENOUGH_DATA && video.videoWidth > 0) {
                const canvasContext = canvas.getContext('2d', { willReadFrequently: true });
                const videoWidth = video.videoWidth;
                const videoHeight = video.videoHeight;
                
                // 设置canvas尺寸与视频相同
                canvas.width = videoWidth;
                canvas.height = videoHeight;
                
                // 绘制视频帧到canvas
                canvasContext.drawImage(video, 0, 0, videoWidth, videoHeight);
                
                try {
                    // 获取图像数据
                    const imageData = canvasContext.getImageData(0, 0, videoWidth, videoHeight);
                    
                    // 使用jsQR库解码条形码
                    const code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: 'dontInvert',
                    });
                    
                    // 如果找到条形码
                    if (code) {
                        console.log('找到条形码:', code.data);
                        barcodeInput.value = code.data;
                        updateStatus('扫描成功!', false, true);
                        
                        // 自动复制到剪贴板
                        navigator.clipboard.writeText(code.data).then(() => {
                            console.log('条形码已复制到剪贴板');
                        }).catch(err => {
                            console.error('无法复制到剪贴板:', err);
                        });
                        
                        stopScanning();
                        return;
                    }
                } catch (e) {
                    console.error('处理图像数据时出错:', e);
                }
            }
            
            // 继续扫描
            animationFrameId = requestAnimationFrame(scanBarcode);
        }
        
        // 页面卸载时停止扫描
        window.addEventListener('beforeunload', () => {
            stopScanning();
        });
    </script>
</body>
</html>