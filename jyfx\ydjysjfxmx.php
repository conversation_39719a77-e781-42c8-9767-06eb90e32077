<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度经营数据分析（明细） - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">月度经营数据分析（明细）</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <label for="start-date">开始日期：</label>
                <input type="date" id="start-date" name="start-date">
                <label for="end-date">结束日期：</label>
                <input type="date" id="end-date" name="end-date">
                <button type="button" id="query-btn">查询</button>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月项目总数</h5>
                            <h2 class="card-text">12个</h2>
                            <p class="text-success">+2个 较上月</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月合同总额</h5>
                            <h2 class="card-text">¥2,500万</h2>
                            <p class="text-success">+15% 较上月</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月平均合同额</h5>
                            <h2 class="card-text">¥208万</h2>
                            <p class="text-success">+8% 较上月</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月项目完成率</h5>
                            <h2 class="card-text">85%</h2>
                            <p class="text-success">+5% 较上月</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目状态分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度项目明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目编号</th>
                                            <th>项目名称</th>
                                            <th>项目类型</th>
                                            <th>合同额</th>
                                            <th>合同收费</th>
                                            <th>预计收款</th>
                                            <th>已到账</th>
                                            <th>差额</th>
                                            <th>项目人数</th>
                                            <th>工资成本</th>
                                            <th>其他成本</th>
                                            <th>总成本</th>
                                            <th>利润率</th>
                                            <th>项目状态</th>
                                            <th>开始日期</th>
                                            <th>预计完成</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>P20240301</td>
                                            <td>某市政道路工程</td>
                                            <td>市政工程</td>
                                            <td>¥800万</td>
                                            <td>¥640万</td>
                                            <td>¥600万</td>
                                            <td>¥600万</td>
                                            <td>¥0</td>
                                            <td>25人</td>
                                            <td>¥75万</td>
                                            <td>¥320万</td>
                                            <td>¥395万</td>
                                            <td>38.3%</td>
                                            <td><span class="badge bg-success">进行中</span></td>
                                            <td>2024-03-01</td>
                                            <td>2024-06-30</td>
                                        </tr>
                                        <tr>
                                            <td>P20240302</td>
                                            <td>某住宅小区建设</td>
                                            <td>房建工程</td>
                                            <td>¥1,200万</td>
                                            <td>¥960万</td>
                                            <td>¥800万</td>
                                            <td>¥800万</td>
                                            <td>¥0</td>
                                            <td>35人</td>
                                            <td>¥105万</td>
                                            <td>¥480万</td>
                                            <td>¥585万</td>
                                            <td>39.1%</td>
                                            <td><span class="badge bg-success">进行中</span></td>
                                            <td>2024-03-05</td>
                                            <td>2024-08-31</td>
                                        </tr>
                                        <tr>
                                            <td>P20240303</td>
                                            <td>某商业广场装修</td>
                                            <td>装修工程</td>
                                            <td>¥500万</td>
                                            <td>¥400万</td>
                                            <td>¥400万</td>
                                            <td>¥400万</td>
                                            <td>¥0</td>
                                            <td>20人</td>
                                            <td>¥60万</td>
                                            <td>¥200万</td>
                                            <td>¥260万</td>
                                            <td>35.0%</td>
                                            <td><span class="badge bg-success">进行中</span></td>
                                            <td>2024-03-10</td>
                                            <td>2024-05-31</td>
                                        </tr>
                                        <tr>
                                            <td>P20240201</td>
                                            <td>某学校教学楼</td>
                                            <td>房建工程</td>
                                            <td>¥900万</td>
                                            <td>¥720万</td>
                                            <td>¥600万</td>
                                            <td>¥550万</td>
                                            <td>¥50万</td>
                                            <td>30人</td>
                                            <td>¥90万</td>
                                            <td>¥360万</td>
                                            <td>¥450万</td>
                                            <td>37.5%</td>
                                            <td><span class="badge bg-success">进行中</span></td>
                                            <td>2024-02-01</td>
                                            <td>2024-07-31</td>
                                        </tr>
                                        <tr>
                                            <td>P20240202</td>
                                            <td>某医院扩建工程</td>
                                            <td>房建工程</td>
                                            <td>¥1,300万</td>
                                            <td>¥1,080万</td>
                                            <td>¥1,100万</td>
                                            <td>¥1,100万</td>
                                            <td>¥0</td>
                                            <td>40人</td>
                                            <td>¥120万</td>
                                            <td>¥520万</td>
                                            <td>¥640万</td>
                                            <td>40.8%</td>
                                            <td><span class="badge bg-success">进行中</span></td>
                                            <td>2024-02-15</td>
                                            <td>2024-09-30</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目利润率分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="profitRateChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目成本构成</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costStructureChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目类型汇总</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目类型</th>
                                            <th>项目数量</th>
                                            <th>合同总额</th>
                                            <th>合同收费</th>
                                            <th>预计收款</th>
                                            <th>已到账</th>
                                            <th>差额</th>
                                            <th>项目人数</th>
                                            <th>工资成本</th>
                                            <th>其他成本</th>
                                            <th>总成本</th>
                                            <th>平均利润率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>房建工程</td>
                                            <td>3个</td>
                                            <td>¥3,400万</td>
                                            <td>¥2,760万</td>
                                            <td>¥2,500万</td>
                                            <td>¥2,450万</td>
                                            <td>¥50万</td>
                                            <td>105人</td>
                                            <td>¥315万</td>
                                            <td>¥1,360万</td>
                                            <td>¥1,675万</td>
                                            <td>39.1%</td>
                                        </tr>
                                        <tr>
                                            <td>市政工程</td>
                                            <td>1个</td>
                                            <td>¥800万</td>
                                            <td>¥640万</td>
                                            <td>¥600万</td>
                                            <td>¥600万</td>
                                            <td>¥0</td>
                                            <td>25人</td>
                                            <td>¥75万</td>
                                            <td>¥320万</td>
                                            <td>¥395万</td>
                                            <td>38.3%</td>
                                        </tr>
                                        <tr>
                                            <td>装修工程</td>
                                            <td>1个</td>
                                            <td>¥500万</td>
                                            <td>¥400万</td>
                                            <td>¥400万</td>
                                            <td>¥400万</td>
                                            <td>¥0</td>
                                            <td>20人</td>
                                            <td>¥60万</td>
                                            <td>¥200万</td>
                                            <td>¥260万</td>
                                            <td>35.0%</td>
                                        </tr>
                                        <tr>
                                            <td>其他工程</td>
                                            <td>7个</td>
                                            <td>¥1,800万</td>
                                            <td>¥1,440万</td>
                                            <td>¥1,300万</td>
                                            <td>¥1,300万</td>
                                            <td>¥0</td>
                                            <td>80人</td>
                                            <td>¥240万</td>
                                            <td>¥720万</td>
                                            <td>¥960万</td>
                                            <td>36.7%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            document.getElementById('start-date').value = formatDate(firstDay);
            document.getElementById('end-date').value = formatDate(lastDay);
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            // 项目类型分布图表
            const projectTypeCtx = document.getElementById('projectTypeChart').getContext('2d');
            new Chart(projectTypeCtx, {
                type: 'pie',
                data: {
                    labels: ['房建工程', '市政工程', '装修工程', '其他工程'],
                    datasets: [{
                        data: [3, 1, 1, 7],
                        backgroundColor: ['#1e88e5', '#e53935', '#43a047', '#ffb300']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 项目状态分布图表
            const projectStatusCtx = document.getElementById('projectStatusChart').getContext('2d');
            new Chart(projectStatusCtx, {
                type: 'pie',
                data: {
                    labels: ['进行中', '已完成', '已暂停', '未开始'],
                    datasets: [{
                        data: [5, 4, 1, 2],
                        backgroundColor: ['#1e88e5', '#43a047', '#ffb300', '#e53935']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 项目利润率分布图表
            const profitRateCtx = document.getElementById('profitRateChart').getContext('2d');
            new Chart(profitRateCtx, {
                type: 'bar',
                data: {
                    labels: ['<30%', '30%-35%', '35%-40%', '40%-45%', '>45%'],
                    datasets: [{
                        label: '项目数量',
                        data: [1, 3, 5, 2, 1],
                        backgroundColor: '#1e88e5'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '项目数量'
                            }
                        }
                    }
                }
            });

            // 项目成本构成图表
            const costStructureCtx = document.getElementById('costStructureChart').getContext('2d');
            new Chart(costStructureCtx, {
                type: 'pie',
                data: {
                    labels: ['工资成本', '材料成本', '设备成本', '其他成本'],
                    datasets: [{
                        data: [25, 40, 20, 15],
                        backgroundColor: ['#1e88e5', '#e53935', '#43a047', '#ffb300']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    </script>
</body>
</html> 