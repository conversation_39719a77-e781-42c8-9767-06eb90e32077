<?php
error_reporting(0);
session_start();
include 'config.php';
$uid = $_SESSION['xinhu_adminid'];
$projectid=$_SESSION['xinhu_projectid'];
$project=$_SESSION['xinhu_project'];

// if (!is_null($projectid)){
//     echo("有值");
// }else{
//     $sql = "SELECT * FROM tuqoa_rydp WHERE dpryxmid=$uid and state='在职' and status=1 LIMIT 1";
//     $result = mysqli_query($link, $sql);
//     if(mysqli_num_rows($result) > 0){
//         //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
//         while($row = mysqli_fetch_assoc($result)){
//             $_SESSION['projectid'] = $row["drxmid"];
//         }
//         $projectid=$row["drxmid"];
//     }
// }
//var_dump($projectid);die;


// if(mysqli_num_rows($result) > 0){
//     while($row = mysqli_fetch_assoc($result)){
//         echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
//     }
// } else {
//     echo "0 results";
// }
 
// 关闭数据库连接
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link href="./css/home.css" rel="stylesheet" type="text/css" />
  </head>
<form id="form1" name="form1" method="post" action="qhxm.php?pagename=xmsyall">
  <p>
    <label>
        当前项目： 
    <select name="projectid">
      <?php
      //var_dump("11");die;
        $sql = "SELECT * FROM tuqoa_gcproject order by id desc";
        $result = mysqli_query($link, $sql);
        if(mysqli_num_rows($result) > 0){
            //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
            while($row = mysqli_fetch_assoc($result)){
                $mr="";
                if($row["id"] == $_SESSION['xinhu_projectid']){
                    $mr="selected";
                }else{
                    $mr="";
                }
      ?>  
      <option value="<?php echo $row["id"]?>" <?php echo $mr?>><?php echo $row["gcname"]?></option>
      <?php
            }
        }
      ?>
    </select>
    </label>
    <input type="submit" name="Submit" value="切换项目" /><?php echo($xmmc)?>
  </p>
</form>
  <body>
    <div class="app">
      <div class="app-header">
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/qizi.png" />
          </div>
          <a href="/?homeurl=ZmxvdyxwYWdlLHJ3Z2MsYXR5cGU9bXl4bSxwbnVtPW15eG0:&homename=5Lu75Yqh5bm.5Zy6&menuid=NTcz" target="_blank"><div>任务广场</div></a>
        </div>
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <a href="/?homeurl=ZmxvdyxwYWdlLHhteGpjYyxhdHlwZT1teXhtLHBudW09bXl4bQ::&homename=5beh6KeG5oq95p!l&menuid=NTc0" target="_blank"><div>巡视</div></a>
        </div>
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div><a href="/?homeurl=ZmxvdyxwYWdlLHB6amwsYXR5cGU9bXl4bSxwbnVtPW15eG0:&homename=5peB56uZ6K6w5b2V&menuid=NTgx" target="_blank">旁站</a></div>
        </div>
        <div class="app-header-item color-1">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div><a href="/?homeurl=ZmxvdyxwYWdlLGpjeXMsYXR5cGU9bXl4bSxwbnVtPW15eG0:&homename=6L!b5Zy66aqM5pS2&menuid=NTgy" target="_blank">进场验收</a></div>
        </div>
        <div class="app-header-item color-1">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div><a href="/?homeurl=ZmxvdyxwYWdlLGdjeXMsYXR5cGU9bXl4bSxwbnVtPW15eG0:&homename=5bel56iL6aqM5pS2&menuid=NjA4" target="_blank">工程验收</a></div>
        </div>
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div><a href="/?homeurl=ZmxvdyxwYWdlLGp6anksYXR5cGU9bXl4bSxwbnVtPW15eG0:&homename=6KeB6K!B5qOA6aqM&menuid=NjA5" target="_blank">见证检验</a></div><div></div>
        </div>
        <div class="app-header-item color-2">
          <div class="item-icons">
            <img src="./image/wen.png" />
          </div>
          <a href="/?homeurl=ZmxvdyxwYWdlLHB4amMsYXR5cGU9bXl4bSxwbnVtPW15eG0:&homename=5bmz6KGM5qOA6aqM&menuid=NTg0" target="_blank"> <div>平行检验</div></a>
        </div>
        <div class="app-header-item color-2">
          <div class="item-icons">
            <img src="./image/wen.png" />
          </div>
          <div>问题管理</div>
        </div>
        <div class="app-header-item color-1">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>项目看板</div>
        </div>
        <!--
        <div class="app-header-item styles-1">
          <div class="item-icons">
            <img src="./image/add.png" />
          </div>
          <div>添加常用</div>
        </div>-->
      </div>

      <div class="app-done">
        <div class="app-done-item">
          <div class="app-done-title">项目待办(2)</div>
          <div class="app-done-table">
            <el-tabs
              v-model="activeName"
              class="demo-tabs"
              @tab-click="handleClick"
            >
                <?php
                $sql="select count(*) as sl from tuqoa_gcinfo where pgcid=$projectid and status=1";//巡检抽查
                $result = mysqli_query($link, $sql);
                if(mysqli_num_rows($result) > 0){
                    while($row = mysqli_fetch_assoc($result)){
                        //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
                        $xjsl=$row["sl"];
                    }
                } else {
                    echo "0 results";
                }
                ?>
              <el-tab-pane name="first">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums"><?php echo $xjsl ?></div>
                    <div>巡视</div>
                  </div>
                </template>
                <?php
                $sql="select * from tuqoa_gcinfo where pgcid=$projectid and status=1 order by id desc";//巡检抽查
                $result = mysqli_query($link, $sql);
                if(mysqli_num_rows($result) > 0){
                    while($row = mysqli_fetch_assoc($result)){
                        //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
                ?>
                <div class="null-status">
                  <div class="notification-item" v-for="item in 1">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title"><a href="/task.php?a=p&num=gczlxun&mid=<?php echo $row["id"] ?>" target="_blank"><?php echo $row["weizhi"] ?></a></div>
                    <div class="notification-name">
                      <span><?php echo $row["selren"] ?></span> <span><?php echo $row["applydt"] ?></span>
                    </div>
                  </div>
                </div>
                <?php
                    }
                }
                ?>
              </el-tab-pane>
              <?php
                $sql="select count(*) as sl from tuqoa_jcys where projectid=$projectid";//进场验收
                $result = mysqli_query($link, $sql);
                if(mysqli_num_rows($result) > 0){
                    while($row = mysqli_fetch_assoc($result)){
                        //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
                        $xjsl=$row["sl"];
                    }
                } else {
                    echo "0 results";
                }
                ?>
              <el-tab-pane name="second">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums"><?php echo $xjsl ?></div>
                    <div>进场验收</div>
                  </div>
                </template>
                <?php
                $sql="select * from tuqoa_jcys where projectid=$projectid order by jcsj desc";//进场验收
                $result = mysqli_query($link, $sql);
                if(mysqli_num_rows($result) > 0){
                    while($row = mysqli_fetch_assoc($result)){
                        //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
                        $xjsl=$row["sl"];
                ?>
                <div class="null-status">
                  <div class="notification-item" v-for="item in 1">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title"><a href="/task.php?a=p&num=jcys&mid=<?php echo $row["id"]?>" target="_blank"><?php echo $row["mc"] ?></a></div>
                    <div class="notification-name">
                      <span><?php echo $row["jlysr"]?></span> <span><?php echo $row["jcsj"]?></span>
                    </div>
                  </div>
                </div>
                <?php
                    }
                }
                ?>
              </el-tab-pane>
              <?php
                $sql="select count(*) as sl from tuqoa_gcys where projectid=$projectid";//工程验收
                $result = mysqli_query($link, $sql);
                if(mysqli_num_rows($result) > 0){
                    while($row = mysqli_fetch_assoc($result)){
                        //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
                        $xjsl=$row["sl"];
                    }
                } else {
                    echo "0 results";
                }
                ?>
              <el-tab-pane name="third">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums"><?php echo $xjsl ?></div>
                    <div>工程验收</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">工程验收</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="fourth">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>旁站</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">旁站</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="jianyan">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>检验</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">检验</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="renwu">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>任务</div>
                  </div>
                </template>
                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">任务</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="problem">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>问题</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">问题</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="shenhe">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>审核</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">审核</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div class="app-done-container">
              <div class="app-done-container-item">
                <div>工作项总数</div>
                <div class="app-done-container-nums">71</div>
              </div>
              <div class="app-done-container-item color-1">
                <div>完成率</div>
                <div class="app-done-container-nums">0%</div>
              </div>
              <div class="app-done-container-item color-1">
                <div>已完成工作项</div>
                <div class="app-done-container-nums">0%</div>
              </div>
              <div class="app-done-container-item color-2">
                <div>未完成工作项</div>
                <div class="app-done-container-nums">0%</div>
              </div>
              <div class="app-done-container-item color-1">
                <div>完成率</div>
                <div class="app-done-container-nums">0%</div>
              </div>
            </div>
          </div>
        </div>

        <div class="app-done-item">
          <div class="app-done-title">我的工作雷达</div>
          <div class="app-done-statistics">
            <div class="app-done-statistics-item">
              <div class="statistics-item-tag">当月统计</div>
              <div class="statistics-item-container">
                <div class="statistics-item-container-item">
                  <div class="item-nums">13</div>
                  <div class="item-text">积分</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">排名</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">岗位排名</div>
                </div>
              </div>
            </div>

            <div class="app-done-statistics-item">
              <div class="statistics-item-tag">项目累计</div>
              <div class="statistics-item-container">
                <div class="statistics-item-container-item">
                  <div class="item-nums">13</div>
                  <div class="item-text">积分</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">排名</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">岗位排名</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="app-done">
        <div class="app-done-item">
          <div class="app-done-title">工作通知</div>
          <div class="notification">
            <div class="notification-item" v-for="item in 5">
              <div class="notification-icon">
                <img src="./image/yan.png" />
              </div>
              <div class="notification-title">1# 东侧 扣件式钢管脚手架</div>
              <div class="notification-name">
                <span>单乐</span> <span>2024-08-01 10:18:41</span>
              </div>
            </div>
          </div>
        </div>

        <div class="app-done-item">
          <div class="app-done-title">今日施工作业面</div>
          <div class="notification">
            <div class="notification-item" v-for="item in 2">
              <div class="notification-icon">
                <img src="./image/yan.png" />
              </div>
              <div class="notification-title">1# 周边绿化工程 建筑节能</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script>
    Vue.createApp({
      setup() {
        return {
          select: "全部",
          value1: Vue.ref(""),
          activeName: Vue.ref("first"),
        };
      },
    })
      .use(ElementPlus)
      .mount(document.querySelector(".app"));
  </script>
</html>
<?php
mysqli_close($link);
?>
