html,
body,
#app {
  height: 100%;
  background-color: #f7f7f7;
  overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;   
}

.app-container {
    height: 100%;
    overflow: auto;
}

.van-cell__title {
  width: 100%;
}

.van-cell__label {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.van-cell .van-icon.van-icon-notes {
  color: #38b2f6;
}

.workbench {
  margin-top: 10px;
}


.workbench-cell .van-cell__title{
    font-weight: bolder;
    font-size: 16px;
}

.workbench-date {
    background-color: #fff;
    padding: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px 0;
}

.workbench-date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;
}

.workbench-date-item-nums {
    font-size: 20px;
    font-weight: bolder;
}


.workbench-date-item-nums.blue {
    color: #34B1FA;
}

.workbench-date-item-nums.green {
    color: #20BF8A;
}


.workbench-date-item-nums.yellow {
    color: #FEAB29;
}

.workbench-date-item-desc {
    font-size: 12px;
    color: var(--van-cell-value-color);
}

.phase {
    margin-top: 10px;
}


.tree-select {
    background-color: #fff;
    display: flex;
}

.tree-select .tree-select-slider {
    width: 6.2rem;
    border-right: 1px solid #eeeeee;
    flex-shrink: 0;
}

.tree-select-slider-item {
    padding: 0.8rem;
    font-size: 0.9rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative;
}

.tree-select-slider-item.active {
    background-color: #E9F6FE;
}

.tree-select-slider-item.active::after {
    content: "";
    position: absolute;
    width: 2px;
    height: 100%;
    background-color: #1B93F0;
    top: 0;
    right: 0;
}

.tree-select-container,.tree-select-container-pane{
    width: 100%;
}

.tree-select-container-pane {
    padding: 0 20px;
    box-sizing: border-box;
}

.tree-select-pane-item {
    padding: 10px 0;
    font-size: 14px;
}

.pane-item-progress {
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.progress-container {
    width: 100%;
    background-color: #FDEFCC;
    height: 10px;
}


.pane-item-progress .progress-text {
    color: #F8A91E;
    flex-shrink: 0;
}