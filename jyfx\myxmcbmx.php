<!DOCTYPE html>
<html lang="zh-CN">
<?php
error_reporting(0);
session_start();
include '../config.php';
$defaultMonth = date('Y-m');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start-month'])) {
    $selectedMonth = $_POST['start-month'];
} else {
    $selectedMonth = $defaultMonth;
}
$nf = substr($selectedMonth, 0, 4);
$uid = $_SESSION['xinhu_adminid'];
$projectid=$_SESSION['xinhu_projectid'];
$project=$_SESSION['xinhu_project'];
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <title>工程项目进度表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        .header h2 {
            color: #2d3748;
            margin: 0;
        }
        .header p {
            color: #718096;
            margin: 5px 0 0;
            font-size: 14px;
        }
        .table-container {
            width: 100%;
            overflow: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            background: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            text-align: left;
        }
        th {
            background-color: #2563eb;
            color: white;
            font-weight: 500;
            width: 200px;
        }
        tr:nth-child(even) {
            background-color: #f8fafc;
        }
        tr:hover {
            background-color: #f7fafc;
        }
        .progress-container {
            display: flex;
            align-items: center;
        }
        .progress-bar {
            width: 60px;
            height: 6px;
            background-color: #e2e8f0;
            border-radius: 3px;
            margin-left: 8px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #10b981;
        }
        .date-range-container {
            margin-bottom: 15px;
        }
        .form-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        #query-btn {
            padding: 5px 15px;
            background-color: #2563eb;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #query-btn:hover {
            background-color: #1d4ed8;
        }
        .project-header {
            background-color: #2d3748;
            color: white;
            font-weight: bold;
        }
        .project-name {
            font-weight: bold;
            background-color: #e2e8f0;
        }
    </style>
    <script language="JavaScript" type="text/javascript">
        function method5(tableid) {
            var uri = 'data:application/vnd.ms-excel;base64,',
                template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
            if (!tableid.nodeType) tableid = document.getElementById(tableid)
            var ctx = {worksheet: 'Worksheet', table: tableid.innerHTML}
            window.location.href = uri + base64(format(template, ctx))
        }
    </script>
</head>
<body>
    <div class="date-range-container">
        <form method="post" action="">
            <div class="form-group">
                <label for="start-month">开始月份:</label>
                <input type="month" id="start-month" name="start-month" 
                       value="<?php echo isset($_POST['start-month']) ? htmlspecialchars($_POST['start-month']) : date('Y-m'); ?>">
                <button type="submit" id="query-btn">提交</button>
                <button type="button" onclick="method5('tableExcel')">导出Excel</button>
            </div>
        </form>
    </div>
    <div class="table-container">
        <?php
        $sql="SELECT * FROM `tuqoa_gcproject` WHERE id=$projectid";
        $result = mysqli_query($link, $sql);
        while ($row = mysqli_fetch_assoc($result)) {
        ?>
        <table id="tableExcel">
            <tr class="project-header">
                <th colspan="2"><?php echo $row["gcname"]; ?> - 项目详情</th>
            </tr>
            <tr>
                <th>建设单位</th>
                <td><?php echo $row["jsname"]; ?></td>
            </tr>
            <tr>
                <th>开工日期</th>
                <td><?php echo $row["jhstartdt"]; ?></td>
            </tr>
            <tr>
                <th>竣工日期</th>
                <td><?php echo $row["jhenddt"]; ?></td>
            </tr>
            <tr>
                <th>合同金额</th>
                <td><?php echo $row["zaojia"]; ?>万</td>
            </tr>
            
            <?php
            $jhjd="";
            $sjjd="";
            $pcyy="";
            $wcl=0;
            $wccz=0;
            $sql1="SELECT * FROM `tuqoa_xmcztjb` where projectid=".$row["id"]." and `sbrq` like '$selectedMonth%' order by id desc";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $jhjd=$row1["jhjd"];
                $sjjd=$row1["sjjd"];
                $pcyy=$row1["pcyy"];
                $wcl=$row1["wcl"];
                $wccz=$row1["wccz"];
            }
            ?>
            <tr>
                <th>计划进度</th>
                <td><?php echo $jhjd; ?></td>
            </tr>
            <tr>
                <th>实际进度</th>
                <td><?php echo $sjjd; ?></td>
            </tr>
            <tr>
                <th>偏差原因</th>
                <td><?php echo $pcyy; ?></td>
            </tr>
            <tr>
                <th>完成率</th>
                <td>
                    <div class="progress-container">
                        <span><?php echo $wcl; ?>%</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: <?php echo $wcl; ?>%"></div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th>本月产值</th>
                <td><?php echo $wccz; ?>万</td>
            </tr>
            
            <?php
            $sql1="select IFNULL(SUM(wccz), 0) as wccznd from tuqoa_xmcztjb where projectid=".$row["id"]." and sbrq like '$nf%'";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $wccznd=$row1["wccznd"];
            }
            ?>
            <tr>
                <th>本年累计</th>
                <td><?php echo $wccznd; ?>万</td>
            </tr>
            
            <?php
            $sql1="select IFNULL(sum(wccz),0) as wcczlj from tuqoa_xmcztjb where projectid=".$row["id"]."";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $wcczlj=$row1["wcczlj"];
            }
            ?>
            <tr>
                <th>累计产值</th>
                <td><?php echo $wcczlj; ?>万</td>
            </tr>
            
            <?php
            $实际直接费=0;
            $应发工资合计=0;
            $实际成本费用=0;
            $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $sql2="SELECT * FROM `tuqoa_hrsalary` WHERE `uname`='".$row1["dpryxm"]."' and `month`='".$selectedMonth."'";
                $result2 = mysqli_query($link, $sql2);
                while ($row2 = mysqli_fetch_assoc($result2)) {
                    $应发工资合计+=$row2["yfgz"];
                }
            }
            
            $员工社保等上缴金额合计=0;
            $sql1="SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and `ys` like '$selectedMonth%'";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $员工社保等上缴金额合计=$row1["hj"];
            }
            
            $预算成本费用=0;
            $预算直接费=0;
            $企业管理费=0;
            $经营业务费=0;
            $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and `sbrq` like '$selectedMonth%'";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $预算成本费用=$row1["yszcbfy"];
                $预算直接费=$row1["yszjf"];
                $企业管理费=$row1["qyglf"];
                $经营业务费=$row1["jyywf"];
            }
            $实际成本费用=$应发工资合计+$员工社保等上缴金额合计+$企业管理费+$经营业务费;
            $实际直接费=$应发工资合计+$员工社保等上缴金额合计;
            ?>
            <tr>
                <th>预算成本</th>
                <td><?php echo $预算成本费用; ?></td>
            </tr>
            <tr>
                <th>实际成本</th>
                <td><?php echo $实际成本费用; ?></td>
            </tr>
            
            <?php
            $成本占比=0;
            $直接费占比=0;
            if($预算成本费用>0){
                 $成本占比=number_format(($实际成本费用/$预算成本费用)*100,2);
                 $直接费占比=number_format(($实际直接费/$预算直接费)*100,2);
            }
            ?>
            <tr>
                <th>成本占比</th>
                <td><?php echo $成本占比; ?>%</td>
            </tr>
            <tr>
                <th>预算直接费</th>
                <td><?php echo $预算直接费; ?></td>
            </tr>
            <tr>
                <th>实际直接费</th>
                <td><?php echo $实际直接费; ?>万</td>
            </tr>
            <tr>
                <th>直接费占比</th>
                <td><?php echo $直接费占比; ?>%</td>
            </tr>
            
            <?php
            $应回收款=0;
            $sql1="SELECT ifnull(sum(yjje),0) as  yjjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `yjsj` like '$selectedMonth%'";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $应回收款=$row1["yjjehj"];
            }
            ?>
            <tr>
                <th>应回收款</th>
                <td><?php echo $应回收款; ?>万</td>
            </tr>
            
            <?php
            $实际收款=0;
            $sql1="SELECT ifnull(sum(ysje),0) as  ysjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `sksj` like '$selectedMonth%'";
            $result1 = mysqli_query($link, $sql1);
            while ($row1 = mysqli_fetch_assoc($result1)) {
                $实际收款=$row1["ysjehj"];
            }
            ?>
            <tr>
                <th>实际收款</th>
                <td><?php echo $实际收款; ?>万</td>
            </tr>
            
            <!-- 添加空行分隔不同项目 -->
            <tr><td colspan="2" style="height: 20px; background-color: white;"></td></tr>
        <?php } ?>
        </table>
    </div>
</body>
</html>