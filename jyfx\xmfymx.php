<!DOCTYPE html>
<html lang="zh-CN">
<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 确保日期格式正确
if (isset($_POST['start-date']) && $_POST['start-date']) {
    $startDate = $_POST['start-date'] . '-01'; // 添加日期部分
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    $endDate = date('Y-m-t', strtotime($_POST['end-date'] . '-01')); // 获取月份的最后一天
}
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月', strtotime($startDate));
        $displayEnd = date('Y年m月', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <title>项目费用明细表</title>
    <style>
        <?php
        // 使用固定的主题色，避免函数调用问题
        $maincolor = '#1389D3';
        $maincolors = '19,137,211'; // RGB值
        ?>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            background-color: #f8fafc;
            z-index: 20;
        }
        .header h2 {
            color: #2d3748;
            margin: 0;
        }
        .header p {
            color: #718096;
            margin: 5px 0 0;
            font-size: 14px;
        }
        .table-container {
            width: 100%;
            height: calc(100vh - 180px); /* 设置固定高度，留出空间给页眉页脚 */
            overflow: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            background: white;
            position: relative;
        }
        table {
            width: 100%;
            min-width: 1800px;
            border-collapse: collapse;
            font-size: 14px;
        }
        thead {
            position: sticky;
            top: 0;
            z-index: 15;
        }
        th {
            background-color: <?php echo $maincolor; ?>;
            color: white;
            padding: 8px 12px;
            text-align: center;
            font-weight: 500;
            white-space: nowrap;
            border: 1px solid rgba(255,255,255,0.2);
            position: sticky;
            top: 0;
        }
        .sub-header th {
            background-color: #3b82f6;
            top: 38px; /* 第一行表头高度 */
        }
        .sub-sub-header th {
            background-color: #60a5fa;
            top: 76px; /* 第一行和第二行表头高度之和 */
        }
        td {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            white-space: nowrap;
            color: #4a5568;
            text-align: right;
        }
        td:first-child, td:nth-child(2), td:nth-child(3) {
            text-align: left;
        }
        tbody tr:hover td {
            background-color: #f7fafc;
        }
        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .footer {
            margin-top: 15px;
            font-size: 12px;
            color: #718096;
            text-align: right;
            position: sticky;
            bottom: 0;
            background-color: #f8fafc;
            padding: 10px 0;
        }
        .text-left {
            text-align: left;
        }
        .text-center {
            text-align: center;
        }
        .highlight {
            font-weight: 500;
            background-color: <?php echo $maincolor; ?>;
            color: white;
        }

        .card-header {
            background-color: <?php echo $maincolor; ?>;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }

        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
        }

        .btn-primary {
            background-color: <?php echo $maincolor; ?>;
            border-color: <?php echo $maincolor; ?>;
        }

        .btn-primary:hover {
            background-color: rgba(<?php echo $maincolors; ?>, 0.8);
            border-color: rgba(<?php echo $maincolors; ?>, 0.8);
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<body>
    <div class="header">
        <h2>项目费用明细表</h2>
        <?php
        // 显示调试信息（仅在开发环境中）
        if (isset($_POST['start-date']) || isset($_POST['end-date'])) {
            echo "<div style='background-color: #e3f2fd; padding: 10px; margin: 10px 0; border-radius: 4px;'>";
            echo "<strong>调试信息:</strong><br>";
            echo "开始日期: " . htmlspecialchars($startDate) . "<br>";
            echo "结束日期: " . htmlspecialchars($endDate) . "<br>";
            echo "POST数据: start-date=" . (isset($_POST['start-date']) ? $_POST['start-date'] : '未设置') .
                 ", end-date=" . (isset($_POST['end-date']) ? $_POST['end-date'] : '未设置') . "<br>";
            echo "</div>";
        }

        // 简化的数据库连接检查
        if (!isset($link) || !$link) {
            echo "<div style='background-color: #ffebee; padding: 10px; margin: 10px 0; border-radius: 4px; color: #c62828;'>";
            echo "<strong>数据库连接错误</strong> - 请检查配置文件";
            echo "</div>";
        } else {
            echo "<div style='background-color: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 4px; color: #2e7d32;'>";
            echo "<strong>数据库连接正常</strong>";
            echo "</div>";
        }
        ?>
    </div>
    <div class="date-range-container">
        <form method="post" action="">
            <div class="form-group">
                <label for="start-date">开始日期:</label>
                <input type="month" id="start-date" name="start-date" 
                       value="<?php echo isset($_POST['start-date']) ? htmlspecialchars($_POST['start-date']) : date('Y-m'); ?>">
                <label for="end-date">结束日期:</label>
                <input type="month" id="end-date" name="end-date" 
                       value="<?php echo isset($_POST['end-date']) ? htmlspecialchars($_POST['end-date']) : date('Y-m'); ?>">
                <button type="submit" id="query-btn">提交</button>
                <button type="button" onclick="method5('tableExcel')">导出Excel</button>
            </div>
        </form>
    </div>
    <div class="table-container">
        <table id="tableExcel" >
            <thead>
                <tr>
                    <th rowspan="3" class="text-center">序号</th>
                    <th rowspan="3" class="text-center">项目名称</th>
                    <th colspan="15">直接费</th>
                    <th colspan="2" rowspan="2">间接费</th>
                    <th rowspan="3">利润（%）</th>
                    <th rowspan="3">税金（8.2%）</th>
                </tr>
                <tr class="sub-header">
                    <th colspan="7">人工费</th>
                    <th colspan="8">其他直接费</th>
                </tr>
                <tr class="sub-sub-header">
                    <th>实发工资</th>
                    <th>公积金（单位+个人）</th>
                    <th>社医保（单位+个人）</th>
                    <th>伙食费</th>
                    <th>采暖费</th>
                    <th>福利费</th>
                    <th>工会经费（工资*2%）</th>
                    
                    <th>购买行政用品</th>
                    <th>办公费</th>
                    <th>折旧费</th>
                    <th>低值易耗品摊销</th>
                    <th>差旅费</th>
                    <th>其他费用</th>
                    <th>中标服务费</th>
                    <th>业务招待费</th>
                    <th>企业管理费</th>
                    
                    <th>经营业务费</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') order by id desc";
                $result = mysqli_query($link, $sql);
                if (!$result) {
                    echo "<tr><td colspan='20' style='color: red; text-align: center;'>SQL查询错误: " . mysqli_error($link) . "</td></tr>";
                    echo "<tr><td colspan='20' style='color: blue; text-align: center;'>SQL语句: " . htmlspecialchars($sql) . "</td></tr>";
                } else if (mysqli_num_rows($result) == 0) {
                    echo "<tr><td colspan='20' style='text-align: center;'>没有找到符合条件的项目数据</td></tr>";
                } else {
                    while ($row = mysqli_fetch_assoc($result)) {
                ?>
                <tr>
                    <td class="text-center highlight"><?php echo $row["id"]?></td>
                    <td class="text-left"><?php echo $row["gcname"]?></td>
                    <?php
                    $actual_salary_total=0;
                    $expected_salary_total=0;
                    $housing_fund_total=0;
                    $meal_allowance_total=0;
                    $heating_fee_total=0;
                    $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            //print_r($row1["dpryxm"]);
                            $sql2="SELECT *  FROM `tuqoa_hrsalary` WHERE `uname`='".mysqli_real_escape_string($link, $row1["dpryxm"])."' and  `month`>='$startDate' and `month`<='$endDate'";
                            $result2 = mysqli_query($link, $sql2);
                            if ($result2) {
                                while ($row2 = mysqli_fetch_assoc($result2)) {
                                    $actual_salary_total+=$row2["sfgz"];
                                    $expected_salary_total+=$row2["yfgz"];
                                    $housing_fund_total+=$row2["zfgjj"]*2;
                                    $meal_allowance_total+=$row2["foodbt"];
                                    $heating_fee_total+=$row2["cnf"];
                                }
                            }
                        }
                    }
                    $date = new DateTime($endDate);
                    $lastDayOfMonth = $date->format('Y-m-t');
                    /////////////////////////////////////////////////项目上缴社保费用明细///////////////////////////////////////////
                    $pension_total=0;
                    $medical_reimbursement_total=0;
                    $sql1="SELECT ifnull(sum(ylxj),0) ylhj,ifnull(sum(yiliaobxxj),0) yiliaobxxjhj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and  `ys`>='$startDate' and `ys`<='$endDate'";
                    //$sql1="SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and `ys` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $pension_total=$row1["ylhj"];
                            $medical_reimbursement_total=$row1["yiliaobxxjhj"];
                        }
                    }
                    /////////////////////////////////////////////////企业上缴社保管理费等//////////////////////////////////////////////////////////
                    //$sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and `sbrq` like '$selectedMonth%'";
                    $welfare_fee_total=0;
                    $office_supplies_total=0;
                    $office_fee_total=0;
                    $depreciation_fee_total=0;
                    $consumables_amortization_total=0;
                    $travel_fee_total=0;
                    $other_fee_total=0;
                    $bidding_service_fee_total=0;
                    $business_entertainment_fee_total=0;
                    $enterprise_management_fee_total=0;
                    $business_operation_fee_total=0;
                    $profit_total=0;
                    $tax_total=0;
                    $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and  `sbrq`>='$startDate' and `sbrq`<='$endDate'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $welfare_fee_total+=$row1["flf"];
                            $office_supplies_total+=$row1["gmxzyp"];
                            $office_fee_total+=$row1["bgf"];
                            $depreciation_fee_total+=$row1["zzjf"];
                            $consumables_amortization_total+=$row1["dzyhptx"];
                            $travel_fee_total+=$row1["clf"];
                            $other_fee_total+=$row1["qtfy"];
                            $bidding_service_fee_total+=$row1["zbfwf"];
                            $business_entertainment_fee_total+=$row1["ywzdf"];
                            $enterprise_management_fee_total+=$row1["qyglf"];
                            $business_operation_fee_total+=$row1["jyywf"];
                            $profit_total+=$row1["lr"];
                            $tax_total+=$row1["sj"];
                        }
                    }
                    ?>
                    <td><?php echo $actual_salary_total?></td>
                    <td><?php echo $housing_fund_total?></td>
                    <td><?php echo $pension_total+$medical_reimbursement_total?></td>
                    <td><?php echo $meal_allowance_total?></td>
                    <td><?php echo $heating_fee_total?></td>
                    <td><?php echo $welfare_fee_total?></td>
                    <td><?php echo round($expected_salary_total*0.02,2)?></td>

                    <td><?php echo $office_supplies_total?></td>
                    <td><?php echo $office_fee_total?></td>
                    <td><?php echo $depreciation_fee_total?></td>
                    <td><?php echo $consumables_amortization_total?></td>
                    <td><?php echo $travel_fee_total?></td>
                    <td><?php echo $other_fee_total?></td>
                    <td><?php echo $bidding_service_fee_total?></td>
                    <td><?php echo $business_entertainment_fee_total?></td>
                    <td><?php echo $enterprise_management_fee_total?></td>
                    <td><?php echo $business_operation_fee_total?></td>


                    <td><?php echo $profit_total?></td>
                    <td><?php echo $tax_total?></td>
                </tr>
                <?php
                    } // End while loop
                }
                ?>
            </tbody>
        </table>
    </div>
</body>
</html>