
<?php
include 'config.php';
session_start();
// 目标页面的URL
$uid = $_SESSION['xinhu_adminid'];
$projectid = $_POST['projectid'];
$pagename = $_GET['pagename'];
$_SESSION['xinhu_projectid'] = $projectid;
$sql = "SELECT gcname FROM tuqoa_gcproject WHERE id=$projectid";
//var_dump($sql);die;
$result = mysqli_query($link, $sql);
if(mysqli_num_rows($result) > 0){
    //echo "id: " . $row["id"]. " - Name: " . $row["gcname"]. "<br>";
    while($row = mysqli_fetch_assoc($result)){
        $_SESSION['xinhu_project']=$row["gcname"];
    }
}
//将默认项目切换到当前项目
$sql="UPDATE `tuqoa_rydp` SET `mrxm`=0 where `dpryxmid`=$uid and `state`='在职'";
mysqli_query($link, $sql);
$sql="UPDATE `tuqoa_rydp` SET `mrxm`=1 where `dpryxmid` =$uid and `drxmid`=$projectid and `state`='在职'";
mysqli_query($link, $sql);


$url = $pagename.".php";
if($pagename=="moble"){
    $url = $pagename.".php?a=1";
}
//header("Location: $url");
mysqli_close($link);
//exit();
?>
<script type="text/javascript">
    var url = "<?php echo $url?>";
    alert("切换项目完成！");
    window.open(url,'_self');
</script>