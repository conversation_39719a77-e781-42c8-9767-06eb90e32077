<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link href="./css/home.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.35/dist/vue.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.7.8/dist/index.full.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/element-plus@2.7.8/dist/index.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="app">
      <div class="app-header">
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/qizi.png" />
          </div>
          <div>任务广场</div>
        </div>
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>巡视</div>
        </div>
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>旁站</div>
        </div>
        <div class="app-header-item color-1">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>进场验收</div>
        </div>
        <div class="app-header-item color-1">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>工程验收</div>
        </div>
        <div class="app-header-item">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>检验</div>
        </div>
        <div class="app-header-item color-2">
          <div class="item-icons">
            <img src="./image/wen.png" />
          </div>
          <div>问题管理</div>
        </div>
        <div class="app-header-item color-1">
          <div class="item-icons">
            <img src="./image/yan.png" />
          </div>
          <div>项目看板</div>
        </div>
        <div class="app-header-item styles-1">
          <div class="item-icons">
            <img src="./image/add.png" />
          </div>
          <div>添加常用</div>
        </div>
      </div>

      <div class="app-done">
        <div class="app-done-item">
          <div class="app-done-title">项目待办(2)</div>
          <div class="app-done-table">
            <el-tabs
              v-model="activeName"
              class="demo-tabs"
              @tab-click="handleClick"
            >
              <el-tab-pane name="first">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>巡视</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">巡视</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="second">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>进场验收</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 2">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">进场验收</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="third">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>工程验收</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">工程验收</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="fourth">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>旁站</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">旁站</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="jianyan">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>检验</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">检验</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="renwu">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>任务</div>
                  </div>
                </template>
                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">任务</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="problem">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>问题</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">问题</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="shenhe">
                <template #label>
                  <div class="demo-tabs-item">
                    <div class="demo-tabs-item-nums">0</div>
                    <div>审核</div>
                  </div>
                </template>

                <div class="null-status">
                  <div class="notification-item" v-for="item in 5">
                    <div class="notification-icon">
                      <img src="./image/yan.png" />
                    </div>
                    <div class="notification-title">审核</div>
                    <div class="notification-name">
                      <span>单乐</span> <span>2024-08-01 10:18:41</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div class="app-done-container">
              <div class="app-done-container-item">
                <div>工作项总数</div>
                <div class="app-done-container-nums">71</div>
              </div>
              <div class="app-done-container-item color-1">
                <div>完成率</div>
                <div class="app-done-container-nums">0%</div>
              </div>
              <div class="app-done-container-item color-1">
                <div>已完成工作项</div>
                <div class="app-done-container-nums">0%</div>
              </div>
              <div class="app-done-container-item color-2">
                <div>未完成工作项</div>
                <div class="app-done-container-nums">0%</div>
              </div>
              <div class="app-done-container-item color-1">
                <div>完成率</div>
                <div class="app-done-container-nums">0%</div>
              </div>
            </div>
          </div>
        </div>

        <div class="app-done-item">
          <div class="app-done-title">我的工作雷达</div>
          <div class="app-done-statistics">
            <div class="app-done-statistics-item">
              <div class="statistics-item-tag">当月统计</div>
              <div class="statistics-item-container">
                <div class="statistics-item-container-item">
                  <div class="item-nums">13</div>
                  <div class="item-text">积分</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">排名</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">岗位排名</div>
                </div>
              </div>
            </div>

            <div class="app-done-statistics-item">
              <div class="statistics-item-tag">项目累计</div>
              <div class="statistics-item-container">
                <div class="statistics-item-container-item">
                  <div class="item-nums">13</div>
                  <div class="item-text">积分</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">排名</div>
                </div>
                <div class="statistics-item-container-item">
                  <div class="item-nums">1</div>
                  <div class="item-text">岗位排名</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="app-done">
        <div class="app-done-item">
          <div class="app-done-title">工作通知</div>
          <div class="notification">
            <div class="notification-item" v-for="item in 5">
              <div class="notification-icon">
                <img src="./image/yan.png" />
              </div>
              <div class="notification-title">1# 东侧 扣件式钢管脚手架</div>
              <div class="notification-name">
                <span>单乐</span> <span>2024-08-01 10:18:41</span>
              </div>
            </div>
          </div>
        </div>

        <div class="app-done-item">
          <div class="app-done-title">今日施工作业面</div>
          <div class="notification">
            <div class="notification-item" v-for="item in 2">
              <div class="notification-icon">
                <img src="./image/yan.png" />
              </div>
              <div class="notification-title">1# 周边绿化工程 建筑节能</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script>
    Vue.createApp({
      setup() {
        return {
          select: "全部",
          value1: Vue.ref(""),
          activeName: Vue.ref("first"),
        };
      },
    })
      .use(ElementPlus)
      .mount(document.querySelector(".app"));
  </script>
</html>
