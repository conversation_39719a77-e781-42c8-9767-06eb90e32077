* {
    padding: 0;
    margin: 0;
}


body,
html,
.app {
    height: 100%;
    background-color: #F1F2F6;
}

 input[type="submit"]

    {
     width: 90px;
    
    padding: 2px;
    color:black;
   
    font-size: 13px;

    }



.app-header {
    background-color: #fff;
    padding: 20px 40px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}


.app-header-item {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 10px;
    border: solid 1px #E7E7E7;
    border-radius: 5px;
    width: calc(100% / 6 - 35px);
    cursor: pointer;
}


.app-header-item.color-1 .item-icons {
    background-color: #22BD89;
}



.app-header-item.color-2 .item-icons {
    background-color: #FBB00F;
}


.app-header-item.styles-1 .item-icons {
    background-color: #F5F5F5;
    border: dashed 1px #c7c7c7;
}

.item-icons {
    width: 40px;
    height: 40px;
    background-color: #39B0FD;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
}


.item-icons img {
    width: 25px;
    height: 25px;
}

.app-done {
    margin-top: 20px;
    display: flex;
    gap: 20px;
}


.app-done-item {
    background-color: #fff;
    padding: 20px 20px 20px 40px;
    width: 50%;

}

.app-done-title {
    display: flex;
    gap: 20px;
    align-items: center;
}


.app-done-table {
    margin-top: 20px;
}

.app-done-title::before {
    width: 15px;
    height: 15px;
    background-color: #1B97ED;
    content: "";
}

.demo-tabs-item {
    text-align: center;
}


.demo-tabs-item-nums {
    font-size: 25px;
}

.demo-tabs {
    --el-tabs-header-height: 70px !important
}


.null-status {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

a {
  color: #000000;
}
.null-status img {
    width: 100px;
}


.app-done-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.app-done-container-item {
    padding: 20px;
    background-color: #EBFAFF;
    display: flex;
    justify-content: space-between;
    border-radius: 5px;
    width: calc(50% - 50px);
}


.app-done-container-nums {
    font-size: 20px;
}


.app-done-container-item.color-1 {
    background-color: #EAFAF0;
}


.app-done-statistics {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}


.app-done-statistics-item {
    border: solid 1px #ffea7e;
}

.statistics-item-tag {
    display: inline-block;
    background-color: #ffea7e;
    padding: 5px 10px;
    margin-top: 10px;
    font-size: 14px;
}


.statistics-item-container {
    display: flex;
    justify-content: space-around;
    padding: 20px 0;
}

.statistics-item-container .item-nums {
    font-size: 25px;
    text-align: center;
    font-weight: bolder;
}


.statistics-item-container .item-text {
    margin-top: 5px;
    color: #686868;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background-color: #1B97ED;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
}


.notification-icon img {
    width: 25px;
    height: 25px;
}


.notification-item {
    display: flex;
    align-items: center;
    gap: 10px;
}


.notification-name {
    display: flex;
    gap: 20px;
    margin-left: auto;
    color: #686868;
}


.notification {
    margin-top: 30px;
    padding: 0 20px;
    gap: 20px;
    display: flex;
    flex-direction: column;
}