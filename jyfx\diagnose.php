<?php
// 项目成本核算页面诊断脚本
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>项目成本核算页面诊断</h1>";

// 1. 检查配置文件
echo "<h2>1. 检查配置文件</h2>";
if (file_exists('../config.php')) {
    echo "✓ config.php 文件存在<br>";
    include '../config.php';
    
    if (isset($link) && $link) {
        echo "✓ 数据库连接成功<br>";
        echo "数据库名: " . DB_NAME . "<br>";
    } else {
        echo "✗ 数据库连接失败: " . mysqli_connect_error() . "<br>";
    }
} else {
    echo "✗ config.php 文件不存在<br>";
}

// 2. 检查数据表
echo "<h2>2. 检查数据表</h2>";
$tables = ['tuqoa_gcproject', 'tuqoa_htgl', 'tuqoa_xmcztjb'];
foreach ($tables as $table) {
    $sql = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($link, $sql);
    if ($result && mysqli_num_rows($result) > 0) {
        echo "✓ 表 $table 存在<br>";
        
        // 检查记录数
        $sql = "SELECT COUNT(*) as count FROM `$table`";
        $result = mysqli_query($link, $sql);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            echo "&nbsp;&nbsp;记录数: " . $row['count'] . "<br>";
        }
    } else {
        echo "✗ 表 $table 不存在<br>";
    }
}

// 3. 检查必要的函数
echo "<h2>3. 检查必要的函数</h2>";

// 定义函数
if (!function_exists('getconfig')) {
    function getconfig($key, $default = '') {
        $configs = array('apptheme' => '#1389D3');
        return isset($configs[$key]) ? $configs[$key] : $default;
    }
    echo "✓ getconfig 函数已定义<br>";
} else {
    echo "✓ getconfig 函数已存在<br>";
}

if (!function_exists('c')) {
    function c($name) {
        if ($name == 'image') {
            return new SimpleImageHelper();
        }
        return null;
    }
    echo "✓ c 函数已定义<br>";
} else {
    echo "✓ c 函数已存在<br>";
}

if (!class_exists('SimpleImageHelper')) {
    class SimpleImageHelper {
        public function colorTorgb($color) {
            if (!empty($color) && (strlen($color) == 7)) {
                $r = hexdec(substr($color, 1, 2));
                $g = hexdec(substr($color, 3, 2));
                $b = hexdec(substr($color, 5));
            } else {
                $r = $g = $b = 0;
            }
            return array($r, $g, $b);
        }
    }
    echo "✓ SimpleImageHelper 类已定义<br>";
} else {
    echo "✓ SimpleImageHelper 类已存在<br>";
}

// 4. 测试函数调用
echo "<h2>4. 测试函数调用</h2>";
try {
    $maincolor = getconfig('apptheme','#1389D3');
    echo "✓ getconfig 调用成功，主题色: $maincolor<br>";
    
    $maincolora = c('image')->colorTorgb($maincolor);
    $maincolors = $maincolora[0].','.$maincolora[1].','.$maincolora[2];
    echo "✓ c('image')->colorTorgb 调用成功，RGB: $maincolors<br>";
} catch (Exception $e) {
    echo "✗ 函数调用失败: " . $e->getMessage() . "<br>";
}

// 5. 检查样本数据查询
echo "<h2>5. 检查样本数据查询</h2>";
$startDate = date('Y-m-01');
$endDate = date('Y-m-t');

// 项目数据
$sql="SELECT COUNT(*) as count FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止')";
$result = mysqli_query($link, $sql);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "✓ 活跃项目数: " . $row['count'] . "<br>";
} else {
    echo "✗ 项目查询失败: " . mysqli_error($link) . "<br>";
}

// 合同数据
$sql="SELECT COALESCE(SUM(zaojia), 0) as htzje FROM `tuqoa_gcproject` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
$result = mysqli_query($link, $sql);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "✓ 当月合同总额: " . $row['htzje'] . " 万<br>";
} else {
    echo "✗ 合同查询失败: " . mysqli_error($link) . "<br>";
}

// 成本数据
$sql="SELECT COALESCE(SUM(wccz), 0) as sjcbzje FROM `tuqoa_xmcztjb` WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
$result = mysqli_query($link, $sql);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "✓ 当月实际成本: " . $row['sjcbzje'] . " 万<br>";
} else {
    echo "✗ 成本查询失败: " . mysqli_error($link) . "<br>";
}

// 6. 检查文件权限
echo "<h2>6. 检查文件权限</h2>";
if (is_readable('xmcbhs.php')) {
    echo "✓ xmcbhs.php 文件可读<br>";
} else {
    echo "✗ xmcbhs.php 文件不可读<br>";
}

if (is_readable('../config.php')) {
    echo "✓ config.php 文件可读<br>";
} else {
    echo "✗ config.php 文件不可读<br>";
}

echo "<h2>诊断完成</h2>";
echo "<p><a href='xmcbhs_simple.php'>访问简化版项目成本核算页面</a></p>";
echo "<p><a href='xmcbhs.php'>访问完整版项目成本核算页面</a></p>";
?>
