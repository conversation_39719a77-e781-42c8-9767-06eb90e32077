<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工动态 - 打卡作弊查询</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        <?php
        // 获取主题色
        $maincolor = getconfig('apptheme','#1389D3');
        $maincolora = c('image')->colorTorgb($maincolor);
        $maincolors = $maincolora[0].','.$maincolora[1].','.$maincolora[2];
        ?>
        .card-header {
            background-color: <?php echo $maincolor; ?>;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
        }
        .btn-primary {
            background-color: <?php echo $maincolor; ?>;
            border-color: <?php echo $maincolor; ?>;
        }
        .btn-primary:hover {
            background-color: rgba(<?php echo $maincolors; ?>, 0.8);
            border-color: rgba(<?php echo $maincolors; ?>, 0.8);
        }
        .table thead th {
            background-color: <?php echo $maincolor; ?>;
            color: white;
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">代打卡查询</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                        <button type="button" onclick="method5('tableExcel')">导出Excel</button>
                    </div>
                </form>
            </div>
           
            
            <div class="row mt-4">
                

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">打卡问题数据清单</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table" id="tableExcel">
                                    <thead>
                                        <tr>
                                            <th>用户ID</th>
                                            <th>姓名</th>
                                            <th>手机串号</th>
                                            <th>打卡次数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT
                                                t.uid AS 'id',
                                                t.device AS 'ch',
                                                COUNT(*) AS 'dkcs'
                                            FROM
                                                tuqoa_kqdkjl t
                                            JOIN (

                                                SELECT device
                                                FROM tuqoa_kqdkjl
                                                WHERE dkdt >= '$startDate' AND dkdt <= '$endDate'
                                                GROUP BY device
                                                HAVING COUNT(DISTINCT uid) > 1
                                            ) s ON t.device = s.device
                                            WHERE
                                                t.dkdt >= '$startDate' AND t.dkdt <= '$endDate'
                                            GROUP BY
                                                t.uid, t.device
                                            ORDER BY
                                                t.device, COUNT(*) DESC";
                                        $xm="";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                $sql1="SELECT name FROM `tuqoa_userinfo` WHERE `id`=".$row["id"];
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    while ($row1 = mysqli_fetch_assoc($result1)) {
                                                        $xm=$row1["name"];
                                                    }
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo $row["id"]; ?></td>
                                            <td><?php echo $xm; ?></td>
                                            <td><?php echo $row["ch"]; ?></td>
                                            <td><?php echo $row["dkcs"]; ?></td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='4'>数据库查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
mysqli_close($link);
?>
    
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
    // 员工变动趋势图表
            const trendCtx = document.getElementById('employeeTrendChart').getContext('2d');
            
            // 使用处理后的数据
            const labels = <?php echo json_encode($shortMonths); ?>;
            const new_employees = <?php echo json_encode($completeNewData); ?>;
            const quit_employees = <?php echo json_encode($completeQuitData); ?>;
            
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '入职',
                        data: new_employees,
                        borderColor: '#1e88e5',
                        backgroundColor: 'rgba(30, 136, 229, 0.1)',
                        fill: true,
                        tension: 0.1
                    }, {
                        label: '离职',
                        data: quit_employees,
                        borderColor: '#e53935',
                        backgroundColor: 'rgba(229, 57, 53, 0.1)',
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '人数'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '月份'
                            }
                        }
                    }
                }
            });


            // 员工结构分布图表
            const structureCtx = document.getElementById('employeeStructureChart').getContext('2d');
            const deptData = <?php echo json_encode($deptData); ?>;
            
            if (deptData.labels.length > 0 && deptData.data.length > 0) {
                new Chart(structureCtx, {
                    type: 'pie',
                    data: {
                        labels: deptData.labels,
                        datasets: [{
                            data: deptData.data,
                            backgroundColor: [
                                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                                '#FF9F40', '#8AC24A', '#607D8B', '#E91E63'
                            ],
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            } else {
                console.error('没有可用的部门数据');
            }
        }
    </script>
</body>
</html> 