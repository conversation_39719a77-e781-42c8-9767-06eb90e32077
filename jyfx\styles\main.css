/* 全局样式 */
:root {
    --primary-color: #1e88e5; /* 蓝色 */
    --secondary-color: #e53935; /* 红色 */
    --success-color: #43a047;
    --warning-color: #ffb300;
    --danger-color: #d32f2f;
    --info-color: #039be5;
    --light-color: #f5f5f5;
    --dark-color: #212121;
    --gray-color: #757575;
    --border-radius: 0.25rem;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --container-padding: 20px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
}

/* 主内容区域 */
.main-content {
    padding: var(--container-padding);
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

/* 日期选择器样式 */
.date-range-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.date-range-container label {
    margin-right: 10px;
    font-weight: 600;
    color: var(--primary-color);
}

.date-range-container input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-right: 15px;
    font-family: inherit;
}

.date-range-container button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.date-range-container button:hover {
    background-color: var(--secondary-color);
}

/* 筛选器样式 */
.filter-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    background-color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.filter-item {
    display: flex;
    align-items: center;
}

.filter-item label {
    margin-right: 10px;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
}

.filter-item .form-select {
    min-width: 150px;
}

.filter-item .btn {
    margin-left: 5px;
}

/* 卡片样式 */
.card {
    margin-bottom: 20px;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background-color: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-title {
    margin: 0;
    font-weight: 600;
    color: var(--primary-color);
}

.card-body {
    padding: 20px;
    color: var(--dark-color);
}

/* 状态卡片 */
.status-card {
    border-left: 4px solid;
    overflow: hidden;
}

.status-card.online, .status-card.valid, .status-card.received {
    border-left-color: var(--success-color);
}

.status-card.offline, .status-card.expired, .status-card.pending {
    border-left-color: var(--danger-color);
}

.status-card.business, .status-card.warning, .status-card.expected {
    border-left-color: var(--warning-color);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 表格样式 */
.table-responsive {
    margin-top: 10px;
}

.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: var(--primary-color);
    border-top: none;
    background-color: rgba(30, 136, 229, 0.1);
}

.table td {
    vertical-align: middle;
}

/* 徽章样式 */
.badge {
    padding: 5px 10px;
    font-weight: 500;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

/* 进度条样式 */
.progress {
    height: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 5px;
    background-color: var(--primary-color);
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .main-content {
        padding: var(--container-padding);
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 992px) {
    .main-content {
        padding: calc(var(--container-padding) * 0.8);
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .chart-container {
        height: 200px;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: calc(var(--container-padding) * 0.6);
    }
    
    .date-range-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-range-container label {
        margin-bottom: 10px;
    }
    
    .date-range-container input[type="date"] {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .date-range-container button {
        width: 100%;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-item {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .filter-item .form-select {
        width: 100%;
    }
    
    .table-responsive {
        margin: 0 -15px;
        width: calc(100% + 30px);
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: calc(var(--container-padding) * 0.4);
    }
    
    .card-header {
        padding: 10px 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .chart-container {
        height: 180px;
    }
    
    .table th, .table td {
        padding: 8px;
        font-size: 14px;
    }
}

/* 打印样式优化 */
@media print {
    .main-content {
        width: 100%;
        max-width: none;
        padding: 0;
    }
    
    .card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .chart-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 页面标题样式 */
h2.mb-4 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* 卡片标题样式 */
.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

.card-header .card-title {
    color: white;
}

/* 数值样式 */
h2.card-text {
    color: var(--primary-color);
    font-weight: bold;
}

.card-body .card-text {
    color: var(--dark-color);
}

/* 趋势指标样式 */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-dark {
    color: var(--dark-color) !important;
}

.text-light {
    color: var(--light-color) !important;
}

.text-gray {
    color: var(--gray-color) !important;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link {
    color: var(--gray-color);
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

/* 模态框样式 */
.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

.modal-footer {
    border-top: none;
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* 表单样式 */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(30, 136, 229, 0.25);
}

/* 刷新时间样式 */
.refresh-time {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-left: 10px;
}

/* 自动刷新开关样式 */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 项目详情样式 */
.project-details {
    margin-top: 20px;
}

.project-details .card {
    margin-bottom: 15px;
}

/* 工作记录样式 */
.work-record {
    margin-top: 10px;
}

.work-record .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 问题跟踪样式 */
.issue-tracking .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 成本核算样式 */
.cost-accounting .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 合同支付样式 */
.contract-payment .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 部门汇总样式 */
.department-summary .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 公司汇总样式 */
.company-summary .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 月度业务样式 */
.monthly-business .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 员工动态样式 */
.employee-dynamics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 证书动态样式 */
.certificate-dynamics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 支付统计样式 */
.payment-statistics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 业务统计样式 */
.business-statistics .table th {
    background-color: rgba(30, 136, 229, 0.1);
}

/* 业务动态样式 */
.business-dynamics .table th {
    background-color: rgba(30, 136, 229, 0.1);
} 