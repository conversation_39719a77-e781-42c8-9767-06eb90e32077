<!DOCTYPE html>
<html lang="zh-CN">
<?php
// 开启错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 检查数据库连接
if (!$link) {
    die("数据库连接失败: " . mysqli_connect_error());
}

// 定义必要的函数，避免页面空白
if (!function_exists('getconfig')) {
    function getconfig($key, $default = '') {
        $configs = array('apptheme' => '#1389D3');
        return isset($configs[$key]) ? $configs[$key] : $default;
    }
}

if (!function_exists('c')) {
    function c($name) {
        if ($name == 'image') {
            return new SimpleImageHelper();
        }
        return null;
    }
}

class SimpleImageHelper {
    public function colorTorgb($color) {
        if (!empty($color) && (strlen($color) == 7)) {
            $r = hexdec(substr($color, 1, 2));
            $g = hexdec(substr($color, 3, 2));
            $b = hexdec(substr($color, 5));
        } else {
            $r = $g = $b = 0;
        }
        return array($r, $g, $b);
    }
}

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
$gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
$selectedMonth = isset($_POST['month-select']) ? $_POST['month-select'] : date('Y-m');
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目成本核算（月度） - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        <?php
        $maincolor = getconfig('apptheme','#1389D3');
        $maincolora = c('image')->colorTorgb($maincolor);
        $maincolors = $maincolora[0].','.$maincolora[1].','.$maincolora[2];
        ?>
        .card {
            margin-bottom: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: <?php echo $maincolor; ?>;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 0.75rem 1rem;
        }
        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
            font-size: 1rem;
        }
        .card-body {
            padding: 1rem;
        }
        .btn-primary {
            background-color: <?php echo $maincolor; ?>;
            border-color: <?php echo $maincolor; ?>;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-normal {
            background-color: #4caf50;
            color: #fff;
        }
        .status-warning {
            background-color: #ff9800;
            color: #fff;
        }
        .status-danger {
            background-color: #f44336;
            color: #fff;
        }
        .filter-container {
            margin-bottom: 1rem;
        }
        .chart-container {
            height: 300px;
        }
        .progress {
            height: 1.5rem;
        }
        .progress-bar {
            font-size: 0.75rem;
            font-weight: 600;
        }
        .refresh-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .cost-trend-up {
            color: #f44336;
        }
        .cost-trend-down {
            color: #4caf50;
        }
        .cost-trend-stable {
            color: #2196f3;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .monthly-cost-chart {
            height: 250px;
        }
        .cost-distribution-chart {
            height: 250px;
        }
        .cost-trend-chart {
            height: 250px;
        }
        .cost-control-chart {
            height: 250px;
        }
        .cost-ratio-chart {
            height: 250px;
        }
        .cost-summary-chart {
            height: 250px;
        }
        .filter-row {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .filter-item {
            margin-right: 1rem;
        }
        .nav-tabs .nav-link {
            color: #495057;
        }
        .nav-tabs .nav-link.active {
            font-weight: 600;
        }
        .tab-content {
            padding: 1rem 0;
        }
        .employee-card {
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1rem;
        }
        .employee-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        .detail-row {
            margin-bottom: 0.5rem;
        }
        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>项目成本核算（月度）</h2>
                <div class="d-flex align-items-center">
                    <span class="refresh-time me-3">最后更新时间: <span id="last-update-time">2023-06-15 14:30:45</span></span>
                </div>
            </div>
            
            <!-- 项目选择和月份选择 -->
            <form method="post" action="">
            <div class="filter-row mb-4">
                <div class="filter-item">
                    <label for="project-select">选择项目：</label>
                    <select id="project-select" class="form-select" style="width: auto; display: inline-block;" name="gcid">
                        <option value="">全部项目</option>
                        <?php
                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                        $result = mysqli_query($link, $sql);
                        if ($result) {
                            while ($row = mysqli_fetch_assoc($result)) {
                                $selected = ($gcid == $row["id"]) ? 'selected' : '';
                                echo '<option value="'.$row["id"].'" '.$selected.'>'.$row["gcname"].'</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="month-select">选择月份：</label>
                    <select name="month-select" id="month-select" class="form-select" style="width: auto; display: inline-block;">
                        <?php
                        // 生成最近12个月的选项
                        for ($i = 0; $i < 12; $i++) {
                            $month = date('Y-m', strtotime("-$i months"));
                            $monthLabel = date('Y年n月', strtotime("-$i months"));
                            $selected = ($selectedMonth == $month) ? 'selected' : '';
                            echo '<option value="'.$month.'" '.$selected.'>'.$monthLabel.'</option>';
                        }
                        ?>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="start-date">开始日期:</label>
                    <input type="date" id="start-date" name="start-date" class="form-control" style="width: auto; display: inline-block;"
                           value="<?php echo htmlspecialchars($startDate); ?>">
                </div>
                <div class="filter-item">
                    <label for="end-date">结束日期:</label>
                    <input type="date" id="end-date" name="end-date" class="form-control" style="width: auto; display: inline-block;"
                           value="<?php echo htmlspecialchars($endDate); ?>">
                </div>
                <button type="submit" id="query-btn" class="btn btn-primary">查询</button>
            </div>
            </form>
            <!-- 月度统计卡片 -->
            <div class="row">
                <?php
                // 添加项目和月份筛选条件
                $project_filter = '';
                if (!empty($gcid)) {
                    $project_filter = " AND projectid = '$gcid'";
                }

                $month_filter = '';
                if (!empty($selectedMonth)) {
                    $month_filter = " AND DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'";
                }

                // 查询当月实际成本
                $sql="SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM `tuqoa_xmcztjb` WHERE 1=1 $project_filter $month_filter";
                $result = mysqli_query($link, $sql);
                $monthly_cost = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $monthly_cost = round($row["monthly_cost"], 2);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">当月实际成本</h5>
                            <h2 class="card-text text-warning">¥<?php echo number_format($monthly_cost, 2); ?>万</h2>
                            <p class="text-muted"><?php echo date('Y年n月', strtotime($selectedMonth)); ?></p>
                        </div>
                    </div>
                </div>

                <?php
                // 查询当月收入
                $income_filter = '';
                if (!empty($gcid)) {
                    $income_filter = " AND projectid = '$gcid'";
                }

                $month_income_filter = '';
                if (!empty($selectedMonth)) {
                    $month_income_filter = " AND DATE_FORMAT(qdsj, '%Y-%m') = '$selectedMonth'";
                }

                $sql="SELECT COALESCE(SUM(fwf), 0) as monthly_income FROM `tuqoa_htgl` WHERE 1=1 $income_filter $month_income_filter";
                $result = mysqli_query($link, $sql);
                $monthly_income = 0;
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $monthly_income = round($row["monthly_income"], 2);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">当月收入</h5>
                            <h2 class="card-text text-success">¥<?php echo number_format($monthly_income, 2); ?>万</h2>
                            <p class="text-muted"><?php echo date('Y年n月', strtotime($selectedMonth)); ?></p>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
                <?php
                // 计算当月盈亏
                $monthly_profit = $monthly_income - $monthly_cost;
                $profit_class = $monthly_profit >= 0 ? 'text-success' : 'text-danger';
                $profit_text = $monthly_profit >= 0 ? '盈利' : '亏损';
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">当月盈亏</h5>
                            <h2 class="card-text <?php echo $profit_class; ?>">¥<?php echo number_format(abs($monthly_profit), 2); ?>万</h2>
                            <p class="text-muted"><?php echo $profit_text; ?></p>
                        </div>
                    </div>
                </div>

                <?php
                // 计算当月成本控制率
                $monthly_control_rate = ($monthly_income > 0) ? round(($monthly_cost / $monthly_income) * 100, 1) : 0;
                $control_class = $monthly_control_rate <= 80 ? 'text-success' : ($monthly_control_rate <= 90 ? 'text-warning' : 'text-danger');
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">当月成本控制率</h5>
                            <h2 class="card-text <?php echo $control_class; ?>"><?php echo $monthly_control_rate; ?>%</h2>
                            <p class="text-muted"><?php echo $monthly_control_rate <= 80 ? '控制良好' : ($monthly_control_rate <= 90 ? '需要关注' : '超支预警'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-4">
                <?php
                $monthlyCostLabels = [];
                $monthlyCostData = [];

                // 构建安全的SQL查询
                $where_conditions = [];
                if (!empty($gcid)) {
                    $where_conditions[] = "projectid = '" . mysqli_real_escape_string($link, $gcid) . "'";
                }
                $where_conditions[] = "sbrq BETWEEN '" . mysqli_real_escape_string($link, $startDate) . "' AND '" . mysqli_real_escape_string($link, $endDate) . "'";

                $sql = "SELECT DATE_FORMAT(sbrq, '%Y-%m') as month, SUM(wccz) as total
                        FROM tuqoa_xmcztjb
                        WHERE " . implode(' AND ', $where_conditions) . "
                        GROUP BY DATE_FORMAT(sbrq, '%Y-%m')
                        ORDER BY month";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $monthlyCostLabels[] = date('m月', strtotime($row['month']));
                        $monthlyCostData[] = (float)$row['total'];
                    }
                } else {
                    // 查询失败时的错误处理
                    echo "<!-- SQL错误: " . mysqli_error($link) . " -->";
                }

                $monthlyCostLabelsJSON = json_encode($monthlyCostLabels);
                $monthlyCostDataJSON = json_encode($monthlyCostData);
                
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container monthly-cost-chart">
                                <canvas id="monthlyCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度收入vs成本对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyIncomeVsCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月度成本明细表 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本明细表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>上报日期</th>
                                            <th>完成产值(万)</th>
                                            <th>完成率(%)</th>
                                            <th>本年累计(万)</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // 添加月度筛选条件
                                        $month_detail_filter = '';
                                        if (!empty($selectedMonth)) {
                                            $month_detail_filter = " AND DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'";
                                        }

                                        $project_detail_filter = '';
                                        if (!empty($gcid)) {
                                            $project_detail_filter = " AND projectid = '$gcid'";
                                        }

                                        $sql="SELECT * FROM `tuqoa_xmcztjb` WHERE 1=1 $project_detail_filter $month_detail_filter ORDER BY sbrq DESC LIMIT 10";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                $status = "正常";
                                                $statusClass = "status-normal";
                                                if ($row["wcl"] < 50) {
                                                    $status = "延期";
                                                    $statusClass = "status-danger";
                                                } elseif ($row["wcl"] < 80) {
                                                    $status = "预警";
                                                    $statusClass = "status-warning";
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($row["project"]); ?></td>
                                            <td><?php echo $row["sbrq"]; ?></td>
                                            <td><?php echo number_format($row["wccz"], 2); ?></td>
                                            <td><?php echo $row["wcl"]; ?>%</td>
                                            <td><?php echo number_format($row["bnljcz"], 2); ?></td>
                                            <td><span class="status-badge <?php echo $statusClass; ?>"><?php echo $status; ?></span></td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='6'>暂无数据</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月度分析图表 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本控制率趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyControlRateChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度项目进度分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyProgressChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本趋势分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-trend-chart">
                                <canvas id="costTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php
            // 生成月度分析图表数据
            $monthlyLabels = [];
            $monthlyCostData = [];
            $monthlyIncomeData = [];
            $monthlyControlRateData = [];

            // 生成最近12个月的数据
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $monthLabel = date('n', strtotime("-$i months")) . '月';
                $monthlyLabels[] = $monthLabel;

                // 查询该月成本
                $cost_filter = '';
                if (!empty($gcid)) {
                    $cost_filter = " AND projectid = '$gcid'";
                }
                $sql_cost = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM tuqoa_xmcztjb WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month' $cost_filter";
                $result_cost = mysqli_query($link, $sql_cost);
                $monthly_cost = 0;
                if ($result_cost) {
                    $row_cost = mysqli_fetch_assoc($result_cost);
                    $monthly_cost = (float)$row_cost['monthly_cost'];
                }
                $monthlyCostData[] = $monthly_cost;

                // 查询该月收入
                $income_filter = '';
                if (!empty($gcid)) {
                    $income_filter = " AND projectid = '$gcid'";
                }
                $sql_income = "SELECT COALESCE(SUM(fwf), 0) as monthly_income FROM tuqoa_htgl WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$month' $income_filter";
                $result_income = mysqli_query($link, $sql_income);
                $monthly_income = 0;
                if ($result_income) {
                    $row_income = mysqli_fetch_assoc($result_income);
                    $monthly_income = (float)$row_income['monthly_income'];
                }
                $monthlyIncomeData[] = $monthly_income;

                // 计算成本控制率
                $control_rate = ($monthly_income > 0) ? round(($monthly_cost / $monthly_income) * 100, 1) : 0;
                $monthlyControlRateData[] = $control_rate;
            }

            // 项目进度分布数据
            $progressLabels = [];
            $progressData = [];
            $progress_filter = '';
            if (!empty($gcid)) {
                $progress_filter = " AND projectid = '$gcid'";
            }
            $month_progress_filter = '';
            if (!empty($selectedMonth)) {
                $month_progress_filter = " AND DATE_FORMAT(sbrq, '%Y-%m') = '$selectedMonth'";
            }

            // 按进度范围分组
            $sql_progress = "SELECT
                CASE
                    WHEN gcjdjd < 25 THEN '0-25%'
                    WHEN gcjdjd < 50 THEN '25-50%'
                    WHEN gcjdjd < 75 THEN '50-75%'
                    WHEN gcjdjd < 100 THEN '75-100%'
                    ELSE '已完成'
                END as progress_range,
                COUNT(*) as count
                FROM tuqoa_xmcztjb
                WHERE 1=1 $progress_filter $month_progress_filter
                GROUP BY progress_range";
            $result_progress = mysqli_query($link, $sql_progress);
            if ($result_progress) {
                while ($row_progress = mysqli_fetch_assoc($result_progress)) {
                    $progressLabels[] = $row_progress['progress_range'];
                    $progressData[] = (int)$row_progress['count'];
                }
            }

            // 如果没有进度数据，使用默认数据
            if (empty($progressData)) {
                $progressLabels = ['0-25%', '25-50%', '50-75%', '75-100%', '已完成'];
                $progressData = [2, 5, 8, 12, 3];
            }
            ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 从PHP传递数据到JavaScript
            const monthlyLabels = <?php echo json_encode($monthlyLabels); ?>;
            const monthlyCostData = <?php echo json_encode($monthlyCostData); ?>;
            const monthlyIncomeData = <?php echo json_encode($monthlyIncomeData); ?>;
            const monthlyControlRateData = <?php echo json_encode($monthlyControlRateData); ?>;
            const progressLabels = <?php echo json_encode($progressLabels); ?>;
            const progressData = <?php echo json_encode($progressData); ?>;

            // 调试信息
            console.log('月度标签:', monthlyLabels);
            console.log('成本数据:', monthlyCostData);
            console.log('收入数据:', monthlyIncomeData);
            console.log('控制率数据:', monthlyControlRateData);
            console.log('进度标签:', progressLabels);
            console.log('进度数据:', progressData);

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载！');
                return;
            }

            // 初始化图表
            initCharts();
        });

        // 初始化图表
        function initCharts() {
            // 月度成本趋势图表
            const monthlyCostCtx = document.getElementById('monthlyCostChart').getContext('2d');
            new Chart(monthlyCostCtx, {
                type: 'line',
                data: {
                    labels: monthlyLabels,
                    datasets: [
                        {
                            label: '月度成本',
                            data: monthlyCostData,
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#f44336',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 月度收入vs成本对比图表
            const monthlyIncomeVsCostCtx = document.getElementById('monthlyIncomeVsCostChart').getContext('2d');
            new Chart(monthlyIncomeVsCostCtx, {
                type: 'bar',
                data: {
                    labels: monthlyLabels,
                    datasets: [
                        {
                            label: '月度收入',
                            data: monthlyIncomeData,
                            backgroundColor: '#4caf50',
                            borderColor: '#388e3c',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: '月度成本',
                            data: monthlyCostData,
                            backgroundColor: '#f44336',
                            borderColor: '#d32f2f',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额 (万元)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 月度成本控制率趋势图表
            const monthlyControlRateCtx = document.getElementById('monthlyControlRateChart').getContext('2d');
            new Chart(monthlyControlRateCtx, {
                type: 'line',
                data: {
                    labels: monthlyLabels,
                    datasets: [{
                        label: '成本控制率',
                        data: monthlyControlRateData,
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#4caf50',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 120,
                            title: {
                                display: true,
                                text: '控制率 (%)'
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            // 月度项目进度分布图表
            const monthlyProgressCtx = document.getElementById('monthlyProgressChart').getContext('2d');
            new Chart(monthlyProgressCtx, {
                type: 'doughnut',
                data: {
                    labels: progressLabels,
                    datasets: [{
                        data: progressData,
                        backgroundColor: [
                            '#f44336', // 0-25% - 红色
                            '#ff9800', // 25-50% - 橙色
                            '#2196f3', // 50-75% - 蓝色
                            '#4caf50', // 75-100% - 绿色
                            '#9c27b0'  // 已完成 - 紫色
                        ],
                        borderColor: '#fff',
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });
        }
    </script>
<?php mysqli_close($link); ?>
</body>
</html> 