<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link href="./css/index.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.35/dist/vue.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.7.8/dist/index.full.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/element-plus@2.7.8/dist/index.min.css"
      rel="stylesheet"
    />
  </head>

  <body>
    <div class="app">
      <div class="ledger">
        <div class="ledger-header">
          <div class="ledger-title">工作台账</div>
          <div class="ledger-options">
            <el-select v-model="select" placeholder="请选择">
              <el-option label="全部" value="全部" />
            </el-select>
          </div>
        </div>
        <div class="ledger-container">
          <div class="ledger-container-item">
            <div class="ledger-container-item-top">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">2</div>
                  <div class="ledger-data-text">巡视</div>
                </div>
              </div>
            </div>
            <div class="ledger-container-item-bottom">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">1</div>
                  <div class="ledger-data-text">旁站</div>
                </div>
              </div>
            </div>
          </div>
          <div class="ledger-container-item color-1">
            <div class="ledger-container-item-top">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">2</div>
                  <div class="ledger-data-text">进场验收</div>
                </div>
              </div>
            </div>
            <div class="ledger-container-item-bottom">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">1</div>
                  <div class="ledger-data-text">工程验收</div>
                </div>
              </div>
            </div>
          </div>
          <div class="ledger-container-item">
            <div class="ledger-container-item-top">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">0</div>
                  <div class="ledger-data-text">见证检验</div>
                </div>
              </div>
            </div>
            <div class="ledger-container-item-bottom">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">0</div>
                  <div class="ledger-data-text">平行检验</div>
                </div>
              </div>
            </div>
          </div>
          <div class="ledger-container-item">
            <div class="ledger-container-item-top color-1">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">0</div>
                  <div class="ledger-data-text">会议</div>
                </div>
              </div>
            </div>
            <div class="ledger-container-item-top color-2">
              <div style="display: flex; align-items: center; gap: 10px">
                <div class="ledger-image">
                  <div class="ledger-image-round">
                    <img src="./image/yan.png" />
                  </div>
                </div>
                <div class="ledger-data">
                  <div class="ledger-data-num">1</div>
                  <div class="ledger-data-text">收发文</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="personnel">
          <div class="ledger-header">
            <div class="ledger-title">人员到岗</div>
            <div class="ledger-options" style="padding-right: 20px">
              <el-date-picker
                v-model="value1"
                type="date"
                placeholder="请选择时间"
                :default-value="new Date()"
              />
            </div>
          </div>
          <div class="personnel-icon">
            <div class="personnel-icon-num">1/2</div>
            <div class="personnel-icon-text">实到/总人数</div>
          </div>
        </div>
        <div class="personnel">
          <div class="ledger-header">
            <div class="ledger-title">积分排名</div>
            <div class="ledger-tag">
              <div class="ledger-tag-text">总得分:13</div>
              <div class="ledger-tag-text">平均分:6.50</div>
            </div>
            <div class="ledger-options" style="padding-right: 20px">
              <el-date-picker
                v-model="value1"
                type="date"
                placeholder="请选择时间"
                :default-value="new Date()"
              />
            </div>
          </div>

          <div class="employee">
            <div class="employee-item">
              <div class="employee-item-number">1</div>
              <div class="avatar-name">单乐</div>
              <div class="employee-user-name" style="width: 150px">单乐</div>
              <div class="employee-user-zhiwei">总监理工程师</div>
              <div class="employee-nums">13</div>
            </div>
            <div class="employee-item">
              <div class="employee-item-number">2</div>
              <div class="avatar-name">晓龙</div>
              <div class="employee-user-name" style="width: 150px">马晓龙</div>
              <div class="employee-user-zhiwei">总监理工程师代表</div>
              <div class="employee-nums">0</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script>
    Vue.createApp({
      setup() {
        return {
          select: "全部",
          value1: Vue.ref(""),
        };
      },
    })
      .use(ElementPlus)
      .mount(document.querySelector(".app"));
  </script>
</html>
