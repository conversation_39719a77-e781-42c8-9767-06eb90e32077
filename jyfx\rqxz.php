<?php
// 获取本月的第一天和最后一天
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');

// 初始化日期变量
$startDate = isset($_POST['start_date']) ? $_POST['start_date'] : $firstDayOfMonth;
$endDate = isset($_POST['end_date']) ? $_POST['end_date'] : $lastDayOfMonth;
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期区间选择示例</title>
    <style>
        <?php
        // 获取主题色
        $maincolor = getconfig('apptheme','#1389D3');
        $maincolora = c('image')->colorTorgb($maincolor);
        $maincolors = $maincolora[0].','.$maincolora[1].','.$maincolora[2];
        ?>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .date-form {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input[type="date"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #e9f7ef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>日期区间选择</h1>
    
    <div class="date-form">
        <form method="post" action="">
            <div class="form-group">
                <label for="start_date">开始日期:</label>
                <input type="date" id="start_date" name="start_date" 
                       value="<?php echo htmlspecialchars($startDate); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="end_date">结束日期:</label>
                <input type="date" id="end_date" name="end_date" 
                       value="<?php echo htmlspecialchars($endDate); ?>" required>
            </div>
            
            <button type="submit">提交</button>
        </form>
    </div>
    
    <?php
    // 检查是否提交了表单
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
        if (strtotime($startDate) > strtotime($endDate)) {
            echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
        } else {
            // 格式化日期用于显示
            $displayStart = date('Y年m月d日', strtotime($startDate));
            $displayEnd = date('Y年m月d日', strtotime($endDate));
            $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
            
            echo '<div class="result">';
            echo "<h3>您选择的日期区间：</h3>";
            echo "<p>开始日期：{$displayStart}</p>";
            echo "<p>结束日期：{$displayEnd}</p>";
            echo "<p>共选择：{$daysDiff} 天</p>";
            
            // 添加本月信息
            $currentMonth = date('Y年m月');
            $monthDays = date('t', strtotime($firstDayOfMonth));
            echo "<h3>本月信息：</h3>";
            echo "<p>当前月份：{$currentMonth}</p>";
            echo "<p>本月天数：{$monthDays} 天</p>";
            echo '</div>';
        }
    }else{
        echo("获取时间方法");
    }
    ?>
</body>
</html>