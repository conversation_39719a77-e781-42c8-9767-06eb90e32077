<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$defaultMonth = date('Y-m');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start-month'])) {
    $selectedMonth = $_POST['start-month'];
    // 可以在这里进行验证，确保格式正确
} else {
    $selectedMonth = $defaultMonth;
}
$nf = substr($selectedMonth, 0, 4);
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <title>项目成本控制汇总表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }

        .main-content {
            /* 使用Bootstrap默认样式，不覆盖 */
        }

        .container-fluid {
            /* 使用Bootstrap默认样式，不覆盖 */
        }

        h2.mb-4 {
            /* 使用Bootstrap默认样式，不覆盖 */
        }

        .date-range-container {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-header {
            background-color: #1389D3;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }

        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
        }

        .btn-primary {
            background-color: #1389D3;
            border-color: #1389D3;
        }

        .btn-primary:hover {
            background-color: rgba(19, 137, 211, 0.8);
            border-color: rgba(19, 137, 211, 0.8);
        }
        .form-group {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .form-group label {
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            white-space: nowrap;
        }

        .form-group input[type="month"] {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input[type="month"]:focus {
            outline: none;
            border-color: #1389D3;
        }

        #query-btn {
            padding: 8px 16px;
            background-color: #1389D3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        #query-btn:hover {
            background-color: rgba(19, 137, 211, 0.8);
        }

        button[onclick] {
            padding: 8px 16px;
            background-color: #10b981;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        button[onclick]:hover {
            background-color: #059669;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 20px;
        }

        .table-wrapper {
            width: 100%;
            height: calc(100vh - 180px);
            min-height: 500px;
            overflow: auto;
        }
        table {
            width: 100%;
            min-width: 1600px;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 13px;
        }

        thead th {
            position: sticky;
            top: 0;
            background: linear-gradient(135deg, #1389D3 0%, #0f7bb8 100%);
            color: white;
            padding: 16px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 12px;
            white-space: nowrap;
            border-right: 1px solid rgba(255,255,255,0.2);
            z-index: 10;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            letter-spacing: 0.5px;
        }

        thead th:first-child {
            border-top-left-radius: 0;
            border-left: none;
        }

        thead th:last-child {
            border-top-right-radius: 0;
            border-right: none;
        }
        tbody td {
            padding: 14px 12px;
            border-bottom: 1px solid #f1f5f9;
            border-right: 1px solid #f1f5f9;
            white-space: nowrap;
            color: #475569;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        tbody td:last-child {
            border-right: none;
        }

        tbody tr:last-child td {
            border-bottom: none;
        }

        tbody tr:hover {
            background: linear-gradient(90deg, rgba(19, 137, 211, 0.05) 0%, rgba(19, 137, 211, 0.02) 100%);
            transform: scale(1.001);
        }

        tbody tr:hover td {
            color: #1e293b;
        }

        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }

        tbody tr:nth-child(even):hover {
            background: linear-gradient(90deg, rgba(19, 137, 211, 0.08) 0%, rgba(19, 137, 211, 0.03) 100%);
        }

        .text-center {
            text-align: center !important;
        }

        .text-right {
            text-align: right !important;
        }

        .nowrap {
            white-space: nowrap !important;
        }

        .highlight {
            background: linear-gradient(135deg, #1389D3 0%, #0f7bb8 100%);
            color: white !important;
            font-weight: 600;
            border-radius: 6px;
            text-align: center;
        }
        .progress-container {
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 120px;
        }

        .progress-bar {
            width: 70px;
            height: 8px;
            background: linear-gradient(90deg, #e2e8f0 0%, #f1f5f9 100%);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            border-radius: 10px;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            min-width: 35px;
        }
        .footer {
            margin-top: 15px;
            font-size: 12px;
            color: #718096;
            text-align: right;
        }
        .nowrap {
            white-space: nowrap;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .highlight {
            font-weight: 500;
        }
        .date-range-container {
            margin-bottom: 15px;
        }
        .form-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        #query-btn {
            padding: 5px 15px;
            background-color: #2563eb;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #query-btn:hover {
            background-color: #1d4ed8;
        }

        /* 使用Bootstrap默认alert样式 */

        /* 滚动条样式 */
        .table-wrapper::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1389D3 0%, #0f7bb8 100%);
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0f7bb8 0%, #0d6eaa 100%);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h2 {
                font-size: 24px;
            }

            .controls-card {
                padding: 20px;
            }

            .form-group {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .table-wrapper {
                height: calc(100vh - 320px);
            }
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">项目成本控制汇总表</h2>

            <!-- 简单测试 -->
            <div class="alert alert-success" style="display:none;" id="test-alert">
                页面加载成功！
            </div>

            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-month">开始月份:</label>
                        <input type="month" id="start-month" name="start-month"
                               value="<?php echo isset($_POST['start-month']) ? htmlspecialchars($_POST['start-month']) : date('Y-m'); ?>">
                        <button type="submit" id="query-btn">提交</button>
                        <button type="button" onclick="method5('tableExcel')">导出Excel</button>
                    </div>
                </form>
            </div>

        <div class="table-container">
            <div class="table-wrapper">
                <table id="tableExcel">
            <thead>
                <tr>
                    <th class="text-center">行号</th>
                    <th>建设单位</th>
                    <th>项目名称</th>
                    <th class="nowrap">开工日期</th>
                    <th class="nowrap">竣工日期</th>
                    <th class="text-right">合同金额</th>
                    <th class="text-center">计划进度</th>
                    <th class="text-center">实际进度</th>
                    <th>偏差原因</th>
                    <th class="text-center">完成率</th>
                    <th class="text-right">本月产值</th>
                    <th class="text-right">本年累计</th>
                    <th class="text-right">累计产值</th>
                    <th class="text-right">预算成本</th>
                    <th class="text-right">实际成本</th>
                    <th class="text-center">成本占比</th>
                    <th class="text-right">预算直接费</th>
                    <th class="text-right">实际直接费</th>
                    <th class="text-center">直接费占比</th>
                    <th class="text-right">应回收款</th>
                    <th class="text-right">实际收款</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') order by id desc";
                $result = mysqli_query($link, $sql);
                $row_number = 1; // 初始化行号计数器
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                ?>
                <tr>
                    <td class="text-center highlight"><?=$row_number?></td>
                    <td><?=$row["jsname"]?></td>
                    <td><?=$row["gcname"]?></td>
                    <td class="nowrap"><?=$row["jhstartdt"]?></td>
                    <td class="nowrap"><?=$row["jhenddt"]?></td>
                    <td class="text-right"><?=$row["zaojia"]?>万</td>
                    <?php
                    $jhjd="";
                    $sjjd="";
                    $pcyy="";
                    $wcl=0;
                    $wccz=0;
                     $sql1="SELECT *  FROM `tuqoa_xmcztjb` where projectid=".$row["id"]." and `sbrq` like '$selectedMonth%' order by id desc";
                     $result1 = mysqli_query($link, $sql1);

                     if ($result1) {
                         while ($row1 = mysqli_fetch_assoc($result1)) {
                             $jhjd=$row1["jhjd"];
                             $sjjd=$row1["sjjd"];
                             $pcyy=$row1["pcyy"];
                             $wcl=$row1["wcl"];
                             $wccz=$row1["wccz"];
                         }
                     }
                    ?>
                    <td class="text-center"><?=$jhjd?></td>
                    <td class="text-center"><?=$sjjd?></td>
                    <td><?=$pcyy?></td>
                    <td class="text-center">
                        <div class="progress-container">
                            <span class="progress-text"><?=$wcl?>%</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?=$wcl?>%"></div>
                            </div>
                        </div>
                    </td>
                    <td class="text-right"><?=$wccz?>万</td>
                    <?php
                    $sql1="select IFNULL(SUM(wccz), 0) as wccznd from tuqoa_xmcztjb where projectid=".$row["id"]." and sbrq like '$nf%'";
                    $result1 = mysqli_query($link, $sql1);
                    $wccznd = 0;
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $wccznd=$row1["wccznd"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$wccznd?>万</td>
                    <?php
                    $sql1="select IFNULL(sum(wccz),0) as wcczlj from tuqoa_xmcztjb where projectid=".$row["id"]."";
                    $result1 = mysqli_query($link, $sql1);
                    $wcczlj = 0;
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $wcczlj=$row1["wcczlj"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$wcczlj?>万</td>
                    <?php
                    //$实际直接费=0;
                    $actual_direct_cost=0;
                    $total_salary=0;
                    $actual_cost=0;
                    $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            //print_r($row1["dpryxm"]);
                            $sql2="SELECT * FROM `tuqoa_hrsalary` WHERE `uname`='".$row1["dpryxm"]."' and `month`='".$selectedMonth."'";
                            //print_r($sql2."<br>");
                            $result2 = mysqli_query($link, $sql2);
                            if ($result2) {
                                while ($row2 = mysqli_fetch_assoc($result2)) {
                                    $total_salary+=$row2["yfgz"];
                                }
                            }
                        }
                    }
                    //福利费用等获取
                    $social_insurance_total=0;
                    $sql1="SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and `ys` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $social_insurance_total=$row1["hj"];
                        }
                    }
                    ?>
                    <?php
                    $budget_cost=0;
                    $budget_direct_cost=0;
                    $management_fee=0;
                    $business_fee=0;
                    $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and `sbrq` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $budget_cost=$row1["yszcbfy"];
                            $budget_direct_cost=$row1["yszjf"];
                            $management_fee=$row1["qyglf"];
                            $business_fee=$row1["jyywf"];
                            //$实际总成本费用=$row1["gmxzyp"]+$row1["bgf"]+$row1["zjf"]+$row1["dzyhptx"]+$row1["clf"]+$row1["qtfy"]+$row1["zbfwf"]+$row1["qyglf"]+$row1["jyywf"]+$row1["lr"]+$row1["sj"];
                        }
                    }
                    $actual_cost=$total_salary+$social_insurance_total+$management_fee+$business_fee;
                    $actual_direct_cost=$total_salary+$social_insurance_total;
                    ?>
                    <td class="text-right"><?=$budget_cost?></td>
                    <?php
                    // 空的PHP代码块，可以删除或添加其他逻辑
                    ?>
                    <td class="text-right"><?=$actual_cost?></td>
                    <?php
                    $cost_ratio=0;
                    $direct_cost_ratio=0;
                    if($budget_cost>0){
                         $cost_ratio=number_format(($actual_cost/$budget_cost)*100,2);
                         $direct_cost_ratio=number_format(($actual_direct_cost/$budget_direct_cost)*100,2);
                    }
                    ?>
                    <td class="text-center"><?=$cost_ratio?>%</td>
                    <td class="text-right"><?=$budget_direct_cost?></td>
                    <td class="text-right"><?=$actual_direct_cost?>万</td>
                    <?php
                    // 空的PHP代码块
                    ?>
                    <td class="text-center"><?=$direct_cost_ratio?>%</td>
                    <?php
                    $expected_payment=0;
                    $sql1="SELECT ifnull(sum(yjje),0) as  yjjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `yjsj` like '$selectedMonth%'";
                    //print_r($sql1."<br>");
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $expected_payment=$row1["yjjehj"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$expected_payment?>万</td>
                    <?php
                    $actual_payment=0;
                    $sql1="SELECT ifnull(sum(ysje),0) as  ysjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `sksj` like '$selectedMonth%'";
                    //print_r($sql1."<br>");
                    $result1 = mysqli_query($link, $sql1);
                    if ($result1) {
                        while ($row1 = mysqli_fetch_assoc($result1)) {
                            $actual_payment=$row1["ysjehj"];
                        }
                    }
                    ?>
                    <td class="text-right"><?=$actual_payment?>万</td>
                    <?php
                    $row_number++; // 增加行号计数器
                    } // 结束while循环
                } else {
                    echo "<tr><td colspan='16'>查询错误: " . mysqli_error($link) . "</td></tr>";
                }
                ?>
                </tbody>
                </table>
            </div>
        </div>

        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示测试提示
            document.getElementById('test-alert').style.display = 'block';
            setTimeout(function() {
                document.getElementById('test-alert').style.display = 'none';
            }, 3000);

            // 添加表格行的悬停效果
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.001)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 优化进度条动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>