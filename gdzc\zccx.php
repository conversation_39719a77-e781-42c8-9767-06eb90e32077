<?php
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $barcode = isset($_POST['barcode']) ? trim($_POST['barcode']) : '';
    
    if (!empty($barcode)) {
        // 这里可以添加对条形码的处理逻辑，比如数据库查询等
        // 示例：只是返回接收到的条形码和一些额外信息
        
        echo "<h2>条形码信息</h2>";
        echo "<p>接收到的条形码: <strong>" . htmlspecialchars($barcode) . "</strong></p>";
        echo "<p>处理时间: " . date('Y-m-d H:i:s') . "</p>";
        
        // 示例：模拟一些处理结果
        $products = [
            '123456789012' => '产品A - 价格: ￥99.00',
            '987654321098' => '产品B - 价格: ￥199.00',
            '456789123456' => '产品C - 价格: ￥299.00'
        ];
        
        if (isset($products[$barcode])) {
            echo "<p>查询结果: " . $products[$barcode] . "</p>";
        } else {
            echo "<p>未找到匹配的产品信息</p>";
        }
    } else {
        echo "<p>错误: 未接收到条形码数据</p>";
    }
} else {
    echo "<p>错误: 请使用POST方法提交数据</p>";
}
?>