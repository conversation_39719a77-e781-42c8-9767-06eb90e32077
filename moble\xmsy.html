<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="stylesheet"
      href="https://fastly.jsdelivr.net/npm/vant@4/lib/index.css"
    />
    <script src="https://fastly.jsdelivr.net/npm/vue@3"></script>
    <script src="https://fastly.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
    <link rel="stylesheet" href="./css/index.css" />
  </head>
  <body>
    <div id="app">
      <van-nav-bar title="项目看板" left-text="返回" left-arrow></van-nav-bar>
      <div class="app-container">
        <van-cell
          icon="notes"
          :title="currentTitle"
          label="宁夏回族自治区银川市金凤区上海西路街道鲁能·陶陶陶陶陶陶陶陶陶"
          @click="show = true"
        >
        </van-cell>
        <div class="workbench">
          <van-cell title="工作台帐" class="workbench-cell">
            <template #value>
              全部
              <van-icon
                name="arrow-down"
                style="margin-left: 5px"
                color="var(--van-cell-value-color)"
              />
            </template>
          </van-cell>
          <div class="workbench-date">
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums blue">0</div>
              <div class="workbench-date-item-desc">巡视</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums blue">0</div>
              <div class="workbench-date-item-desc">旁站</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums green">0</div>
              <div class="workbench-date-item-desc">进场验收</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums green">0</div>
              <div class="workbench-date-item-desc">工程验收</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums yellow">0</div>
              <div class="workbench-date-item-desc">见证检验</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums blue">0</div>
              <div class="workbench-date-item-desc">平行检验</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums green">0</div>
              <div class="workbench-date-item-desc">会议</div>
            </div>
            <div class="workbench-date-item">
              <div class="workbench-date-item-nums yellow">0</div>
              <div class="workbench-date-item-desc">收发文</div>
            </div>
          </div>
        </div>

        <div class="phase">
          <van-cell title="阶段工作" class="workbench-cell"> </van-cell>

          <div class="tree-select">
            <div class="tree-select-slider">
              <div
                :class="['tree-select-slider-item',item.value === currentActive ? 'active' :'']"
                v-for="item in options"
                @click="currentActive = item.value"
              >
                {{item.name}}
              </div>
            </div>
            <div class="tree-select-container">
              <!-- 工程准备阶段 -->
              <div
                class="tree-select-container-pane"
                v-if="currentActive === '工程准备阶段'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>
              <!-- 施工阶段 -->
              <div
                class="tree-select-container-pane"
                v-if="currentActive === '施工阶段'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>
              <!-- 竣工验收阶段 -->
              <div
                class="tree-select-container-pane"
                v-if="currentActive === '竣工验收阶段'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>
              <!-- 工程质量 -->
              <div
                class="tree-select-container-pane"
                v-if="currentActive === '工程质量'"
              >
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理招标文件及答疑</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理中标通知书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理合同</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">监理部成立文件</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
                <div class="tree-select-pane-item">
                  <div class="pane-item-text">项目总监任命书</div>
                  <div class="pane-item-progress">
                    <div class="progress-container"></div>
                    <div class="progress-text">未开始</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <van-popup
        v-model:show="show"
        :style="{padding:'10px 0', width:'500px' }"
      >
        <van-radio-group v-model="currentTitle">
          <van-cell-group inset>
            <van-cell
              v-for="item in selectOptions"
              :title="item.title"
              clickable
              @click="currentTitle = item.name;show = false"
            >
              <template #right-icon>
                <van-radio :name="item.name" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </van-popup>
    </div>
  </body>

  <script>
    // 在 #app 标签下渲染一个按钮组件
    const app = Vue.createApp({
      setup() {
        const options = Vue.ref([
          {
            name: "工程准备阶段",
            value: "工程准备阶段",
          },
          {
            name: "施工阶段",
            value: "施工阶段",
          },
          {
            name: "竣工验收阶段",
            value: "竣工验收阶段",
          },
          {
            name: "工程质量",
            value: "工程质量",
          },
        ]);

        const selectOptions = [
          {
            name: "绿地地产",
            title: "绿地地产",
          },
          {
            name: "项目经理",
            title: "项目经理",
          },
          {
            name: "总监",
            title: "总监",
          },
          {
            name: "总监代表",
            title: "总监代表",
          },
          {
            name: "专业工程师",
            title: "专业工程师",
          },
          {
            name: "资料员",
            title: "资料员",
          },
          {
            name: "监里员",
            title: "监里员",
          },
        ];

        const currentActive = Vue.ref("工程准备阶段");

        const show = Vue.ref(false);

        const currentTitle = Vue.ref("绿地地产");

        return {
          options,
          currentActive,
          show,
          selectOptions,
          currentTitle,
        };
      },
    });

    app.use(vant);

    app.use(vant.Lazyload);

    app.mount("#app");
  </script>
</html>
