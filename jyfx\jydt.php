<?php
include '../config.php';

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    // 验证日期格式是否正确
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    // 验证日期格式是否正确
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营动态 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        <?php
        // 使用固定的主题色，避免函数调用问题
        $maincolor = '#1389D3';
        $maincolors = '19,137,211'; // RGB值
        ?>
        .card-header {
            background-color: <?php echo $maincolor; ?>;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
        }
        .btn-primary {
            background-color: <?php echo $maincolor; ?>;
            border-color: <?php echo $maincolor; ?>;
        }
        .btn-primary:hover {
            background-color: rgba(<?php echo $maincolors; ?>, 0.8);
            border-color: rgba(<?php echo $maincolors; ?>, 0.8);
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">经营动态</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">近期合同信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>合同编号</th>
                                            <th>客户名称</th>
                                            <th>合同金额</th>
                                            <th>签订日期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    $sql="SELECT * FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate' order by qdsj desc";
                                    $result = mysqli_query($link, $sql);
                                    if ($result && mysqli_num_rows($result) > 0) {
                                        while ($row = mysqli_fetch_assoc($result)) {
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($row["htbh"]);?></td>
                                            <td><a href="/task.php?a=p&num=htgl&mid=<?php echo $row["id"]?>" target="_blank"><?php echo htmlspecialchars($row["htmc"]);?></a></td>
                                            <td><?php echo htmlspecialchars($row["fwf"]);?></td>
                                            <td><?php echo htmlspecialchars($row["qdsj"]);?></td>
                                        </tr>
                                    <?php
                                        } // End while loop
                                    } else {
                                        echo "<tr><td colspan='4'>没有找到合同数据</td></tr>";
                                    }
                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                // 查询总人数
                $zrs = 0;
                $sql="SELECT COUNT(*) as zrs FROM `tuqoa_userinfo` where state<>5";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $zrs=$row["zrs"];
                    }
                }

                // 查询本月入职人数
                $byrz = 0;
                $sql="SELECT count(*) as byrz FROM `tuqoa_userinfo` WHERE `workdate`>='$startDate' and `workdate`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $byrz=$row["byrz"];
                    }
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">人员信息统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6>总人数</h6>
                                            <h3><?php echo $zrs; ?></h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6>本月新增</h6>
                                            <h3><?php echo $byrz; ?></h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <h6>部门分布</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 30%">技术部 30%</div>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 25%">市场部 25%</div>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 20%">运营部 20%</div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 25%">其他 25%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
mysqli_close($link);
?>
</body>
</html> 