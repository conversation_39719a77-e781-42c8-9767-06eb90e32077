<?php
include '../config.php';

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 验证日期格式
if (isset($_POST['start-date']) && $_POST['start-date']) {
    // 验证日期格式是否正确
    if (DateTime::createFromFormat('Y-m-d', $_POST['start-date']) !== false) {
        $startDate = $_POST['start-date'];
    }
}
if (isset($_POST['end-date']) && $_POST['end-date']) {
    // 验证日期格式是否正确
    if (DateTime::createFromFormat('Y-m-d', $_POST['end-date']) !== false) {
        $endDate = $_POST['end-date'];
    }
}

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;

        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营动态 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 页面特定样式 - 只保留必要的自定义样式 */
        .department-progress {
            background: rgba(248, 249, 250, 0.5);
            padding: 12px 15px;
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .department-progress:hover {
            background: rgba(248, 249, 250, 0.8);
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .department-label {
            color: #495057;
            font-size: 0.95rem;
        }

        .department-percentage {
            font-size: 1rem;
            min-width: 40px;
            text-align: right;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 合同表格特定样式 */
        .contract-amount .amount-value {
            font-weight: 700;
            color: #28a745;
            font-size: 1.1rem;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
            padding: 6px 12px;
            border-radius: 8px;
            display: inline-block;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .no-data {
            padding: 40px 20px;
        }

        .no-data-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #6c757d;
        }

        .no-data-content i {
            font-size: 3rem;
            opacity: 0.5;
            margin-bottom: 10px;
        }

        .no-data-content p {
            font-size: 1rem;
            font-weight: 500;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .date-range-container .form-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-range-container label {
                margin-bottom: 5px;
            }

            .department-progress {
                padding: 10px 12px;
            }

            .department-label {
                font-size: 0.9rem;
            }

            .department-percentage {
                font-size: 0.9rem;
            }

            .contract-header {
                padding: 10px 8px;
                font-size: 0.8rem;
            }

            .contract-row td {
                padding: 10px 8px;
            }

            .contract-name {
                max-width: 200px;
            }

            .contract-link {
                font-size: 0.9rem;
                padding: 4px 6px;
            }

            .contract-amount .amount-value {
                font-size: 1rem;
                padding: 4px 8px;
            }

            .contract-date .date-info {
                font-size: 0.8rem;
                padding: 4px 8px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bx bx-bar-chart-alt-2 me-2"></i>
                经营动态
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="bx bx-time me-1"></i>
                    最后更新: <span id="last-update-time"><?php echo date('Y-m-d H:i:s'); ?></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 日期区间选择器 -->
        <div class="date-range-container">
            <form method="post" action="">
                <div class="form-group">
                    <label for="start-date">开始日期:</label>
                    <input type="date" id="start-date" name="start-date" 
                           value="<?php echo htmlspecialchars($startDate); ?>">
                    <label for="end-date">结束日期:</label>
                    <input type="date" id="end-date" name="end-date" 
                           value="<?php echo htmlspecialchars($endDate); ?>">
                    <button type="submit" id="query-btn">
                        <i class="bx bx-search me-1"></i>查询
                    </button>
                </div>
            </form>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">近期合同信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table contract-table">
                                <thead>
                                    <tr>
                                        <th class="contract-header">
                                            <i class="bx bx-hash me-1"></i>合同编号
                                        </th>
                                        <th class="contract-header">
                                            <i class="bx bx-building me-1"></i>客户名称
                                        </th>
                                        <th class="contract-header">
                                            <i class="bx bx-money me-1"></i>合同金额
                                        </th>
                                        <th class="contract-header">
                                            <i class="bx bx-calendar me-1"></i>签订日期
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                $sql="SELECT * FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate' order by qdsj desc";
                                $result = mysqli_query($link, $sql);
                                if ($result && mysqli_num_rows($result) > 0) {
                                    while ($row = mysqli_fetch_assoc($result)) {
                                ?>
                                    <tr class="contract-row">
                                        <td class="contract-id">
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($row["htbh"]);?></span>
                                        </td>
                                        <td class="contract-name">
                                            <a href="/task.php?a=p&num=htgl&mid=<?php echo $row["id"]?>" target="_blank" class="contract-link">
                                                <i class="bx bx-link-external me-1"></i>
                                                <?php echo htmlspecialchars($row["htmc"]);?>
                                            </a>
                                        </td>
                                        <td class="contract-amount">
                                            <span class="amount-value">¥<?php echo htmlspecialchars($row["fwf"]);?></span>
                                        </td>
                                        // 修改日期格式为短日期
                                        <td class="contract-date">
                                            <div class="date-info">
                                                <i class="bx bx-time me-1"></i>
                                                <?php echo date('m-d', strtotime($row["qdsj"]));?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php
                                    } // End while loop
                                } else {
                                    echo '<tr><td colspan="4" class="text-center no-data">
                                            <div class="no-data-content">
                                                <i class="bx bx-file-blank mb-2"></i>
                                                <p class="mb-0">没有找到合同数据</p>
                                            </div>
                                          </td></tr>';
                                }
                                ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            // 查询总人数
            $zrs = 0;
            $sql="SELECT COUNT(*) as zrs FROM `tuqoa_userinfo` where state<>5";
            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $zrs=$row["zrs"];
                }
            }

            // 查询本月入职人数
            $byrz = 0;
            $sql="SELECT count(*) as byrz FROM `tuqoa_userinfo` WHERE `workdate`>='$startDate' and `workdate`<='$endDate'";
            $result = mysqli_query($link, $sql);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $byrz=$row["byrz"];
                }
            }
            ?>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">人员信息统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card stat-card stat-card-primary">
                                    <div class="card-body">
                                        <i class="fas fa-users stat-icon"></i>
                                        <h5 class="card-title">总人数</h5>
                                        <h2 class="card-text"><?php echo $zrs; ?></h2>
                                        <p class="stat-info">在职员工总数</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card stat-card stat-card-success">
                                    <div class="card-body">
                                        <i class="fas fa-user-plus stat-icon"></i>
                                        <h5 class="card-title">本月新增</h5>
                                        <h2 class="card-text"><?php echo $byrz; ?></h2>
                                        <p class="stat-info">新入职员工</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6 class="mb-3 fw-bold text-dark">部门分布</h6>
                            <div class="department-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="department-label fw-semibold">技术部</span>
                                    <span class="department-percentage fw-bold text-primary">30%</span>
                                </div>
                                <div class="progress" style="height: 12px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 30%; border-radius: 6px;"></div>
                                </div>
                            </div>
                            <div class="department-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="department-label fw-semibold">市场部</span>
                                    <span class="department-percentage fw-bold text-success">25%</span>
                                </div>
                                <div class="progress" style="height: 12px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 25%; border-radius: 6px;"></div>
                                </div>
                            </div>
                            <div class="department-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="department-label fw-semibold">运营部</span>
                                    <span class="department-percentage fw-bold text-info">20%</span>
                                </div>
                                <div class="progress" style="height: 12px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 20%; border-radius: 6px;"></div>
                                </div>
                            </div>
                            <div class="department-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="department-label fw-semibold">其他部门</span>
                                    <span class="department-percentage fw-bold text-warning">25%</span>
                                </div>
                                <div class="progress" style="height: 12px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 25%; border-radius: 6px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新最后更新时间
            updateLastUpdateTime();

            // 每30秒更新一次时间
            setInterval(updateLastUpdateTime, 30000);
        });
    </script>
<?php
mysqli_close($link);
?>
</body>
</html>