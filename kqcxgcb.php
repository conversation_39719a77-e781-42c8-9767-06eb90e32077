<!DOCTYPE html>
<!--
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<html>
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
        <title></title>
    </head>
    <body>
        <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
        <?php
        include 'config.php';
        //$strDate = date('Y-m-d 00:00:00');
        $endDate = date('Y-m-d 23:59:59');
        $currentDate = date('Y-m-d 00:00:00');
        $strDate = date('Y-m-d 00:00:00', strtotime("-1 month", strtotime($currentDate)));
        if($_REQUEST['strDate']!=null) {
            $strDate=$_REQUEST['strDate'];
        }
        if($_REQUEST['endDate']!=null) {
            $endDate=$_REQUEST['endDate'];
        }
        
        echo $sql1;
        ?>
        <form id="form1" name="form1" method="post" action="">
            <label>
                <input type="text" name="strDate"  size="20" title="请输入开始时间" value="<?php echo $strDate;?>"/>
                <input type="text" name="endDate"  size="20" title="请输入开始时间" value="<?php echo $endDate;?>"/>
            </label>
            <label>
            <input type="submit" name="Submit" value="查询" />
            <button type="button" onclick="method5('tableExcel')">导出Excel</button>
            </label>
          </form>
          <p id="myp">
        <table id="tableExcel" width="100%" border="1" cellspacing="0" cellpadding="0">
            <tr align="center">
              <td width="40"><strong>姓名</strong></td>
              <td width="150"><strong>所在项目</strong></td>
              <td width="50">正常</td>
              <td width="50">迟到</td>
              <td width="50">早退</td>
              <td width="50">加班（小时）</td>
              <td width="50">请假<br>（小时）</td>
              <td width="50">外出</td>
              <td width="50">异常</td>
              <td width="50">应上班</td>
              <td width="50">已上班</td>
              <td width="50"></td>
            </tr>
            <?php
            $sql="SELECT * FROM `tuqoa_bdksz` WHERE `yf`>='$strDate' and `yf`<='$endDate' LIMIT 1";
            $bdkts=0;
            $sbts=0;
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $bdkts=$row["ts"];
                $sbts=$row["sbts"];
            }
            //$sql = "SELECT * FROM tuqoa_userinfo where state<>'5' order by deptname";
            $sql = "SELECT * FROM tuqoa_userinfo where state<>'5' and iskq=1 and deptname in ('工程管理部') order by deptname";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $id=$row["id"];
                
                $state_zc=0;
                $sql1="SELECT count(*) as state_zc FROM tuqoa_kqanay  where uid='$id' and (dt>='$strDate' and dt<='$endDate') and state='正常' and iswork='1' and states is null";
                //echo($sql1.'<BR>');
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $state_zc=$row1["state_zc"];
                
               
                $sql1="SELECT count(*) as state_cd FROM tuqoa_kqanay  where uid='$id' and (dt>='$strDate' and dt<='$endDate') and state='迟到' and states is null";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $state_cd=$row1["state_cd"];
                
                $sql1="SELECT count(*) as state_zt FROM tuqoa_kqanay  where uid='$id' and (dt>='$strDate' and dt<='$endDate') and state='早退'";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $state_zt=$row1["state_zt"];
                
                $state_wdk=0;
                $sql1="SELECT count(*) as state_wdk FROM tuqoa_kqanay  where uid='$id' and (dt>='$strDate' and dt<='$endDate') and state='未打卡' and states is null and iswork='1'";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $state_wdk=$row1["state_wdk"];
                
                $sql1="SELECT SUM(totals) as jbhj FROM `tuqoa_kqinfo` where uid='$id' and `status`='1' and (stime>='$strDate' and stime<='$endDate') and kind='加班'";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $jbhj=round($row1["jbhj"]/8,1);
                $jbhjxs=$row1["jbhj"];
                
                $sql1="SELECT SUM(totals) as qjhj FROM `tuqoa_kqinfo` where uid='$id' and `status`='1' and (stime>='$strDate' and stime<='$endDate') and kind='请假'";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $qjhj=$row1["qjhj"];
                
                //$sql1="SELECT count(*) as wczh FROM `tuqoa_kqout` where uid='$id' and status='1' and (outtime>='$strDate' and outtime<='$endDate')";
                //$sql1="SELECT count(*) as wczh FROM `tuqoa_kqout` where `uid`='$id' and atype='外出' and (applydt>='$strDate' and applydt<='$endDate')";
                $sql1="SELECT count(*) as wczh FROM tuqoa_kqanay where uid='$id'  and (dt>='$strDate' and dt<='$endDate') and states='外出' and `iswork`=1";
                $wczh=0;
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $wczh=$row1["wczh"];
                
                //select count(*) as yccs from tuqoa_kqerr where uid='124' and (dt>='2024-09-23 00:00:00' and dt<='2024-09-30 23:59:59')
                $sql1="select count(*) as ychj from tuqoa_kqerr where uid='$id' and `status`=1 and (dt>='$strDate' and dt<='$endDate')";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $ychj=$row1["ychj"];
                
                //$sql1="SELECT SUM(timesb) as ysbhj FROM tuqoa_kqanay where uid='$id' and (dt>='$strDate' and dt<='$endDate')";
                $sql1="SELECT count(*) as ysbhj FROM tuqoa_kqanay where uid='$id' and (dt>='$strDate' and dt<='$endDate') and iswork=1 ";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $ysbhj=round(($row1["ysbhj"]-$state_wdk+$ychj)/4,1)-$jbhj;
                
                
                $sql1="SELECT SUM(timeys) as sbhj FROM tuqoa_kqanay where uid='$id' and timeys>0 and (dt>='$strDate' and dt<='$endDate') and iswork=1";
                $result1 = mysqli_query($link, $sql1);
                $row1 = mysqli_fetch_assoc($result1);
                $sbhj=$row1["sbhj"];
                
                $ysbjs=0;
                $ysbjs=round(($state_zc+$wczh)/4,1);
                
                
            ?>
            <tr>
              <td>&nbsp;<?php echo $row["name"];?></td>
              <td>
              <?php
              $xm=$row["name"];
              $sql1="select * from tuqoa_rydp where dpryxm='$xm' and state='在职'";
              $result1 = mysqli_query($link, $sql1);
              while ($row1 = mysqli_fetch_assoc($result1)) {
                  //echo '<p>';
                  echo $row1["drxm"],'(',$row1["sfqz"],')'."(调动日期：)".$row1["ddrq"];
                  echo '<br>';
                 
              }
              ?>
              </td>
              <td>&nbsp;<?php echo $state_zc;?></td>
              <td>&nbsp;<?php echo $state_cd;?></td>
              <td>&nbsp;<?php echo $state_zt;?></td>
              <td>&nbsp;<?php echo $jbhj."($jbhjxs)";?></td>
              <td>&nbsp;<?php echo $qjhj;?></td>
              <td>&nbsp;<?php echo $wczh;?></td>
              <td>&nbsp;<?php echo $ychj;?></td>
              <td>&nbsp;<?=$sbts?></td>
              <td>&nbsp;<?php echo $ysbjs;?></td>
              <td>&nbsp;<?//=$state_wdk-($bdkts*4)?></td>
            </tr>
            <?php
            }
            mysqli_close($link);
            ?>
            
          </table>
          </p>
    </body>
</html>
