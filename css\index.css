* {
    padding: 0;
    margin: 0;
}


body,
html,
.app {
    height: 100%;
    background-color: #F1F2F6;
}

.app {
    padding: 20px;
    box-sizing: border-box;
}

.ledger {
    background-color: #fff;
    padding: 30px;

}


.ledger-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ledger-options {
    width: 200px;
}

.ledger-container {
    margin-top: 50px;
    display: flex;
    padding-bottom: 50px;
}


.ledger-image {
    width: 60px;
    height: 60px;
    background-color: #C8F1FA;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}


.ledger-image-round {
    content: "";
    width: 50px;
    height: 50px;
    background-color: #34B2FD;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    display: flex;
}

.ledger-image-round img {
    width: 30px;
    height: 30px;
}

.ledger-container-item {
    display: flex;
    flex-direction: column;
    gap: 50px;
    width: 100%;
    align-items: center;
    border-right: solid 1px #E8E8E8;
}


.ledger-data {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ledger-data .ledger-data-num {
    font-size: 25px;
    text-align: center;
    color: #55A4D0;
    font-weight: bolder;
}


.ledger-container-item:last-child {
    border-right: none;
}

.ledger-container-item.color-1 .ledger-image-round,
.ledger-container-item-top.color-1 .ledger-image-round {
    background-color: #23C48E;
}


.ledger-container-item.color-1 .ledger-data-num,
.ledger-container-item-top.color-1 .ledger-data-num {
    color: #23C48E;
}


.ledger-container-item.color-1 .ledger-image,
.ledger-container-item-top.color-1 .ledger-image {
    background-color: #D0F8EA;
}


.ledger-container-item.color-2 .ledger-image-round,
.ledger-container-item-top.color-2 .ledger-image-round {
    background-color: #FBB111;
}


.ledger-container-item.color-2 .ledger-data-num,
.ledger-container-item-top.color-2 .ledger-data-num {
    color: #FBB111;
}


.ledger-container-item.color-2 .ledger-image,
.ledger-container-item-top.color-2 .ledger-image {
    background-color: #FFF1C7;
}

.footer {
    margin-top: 20px;
    display: flex;
    gap: 20px;
}

.personnel {
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    width: 50%;
}


.personnel-icon {
    width: 150px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: #0A9AFD;
    border-radius: 50%;
    margin: 30px auto;
    color: #fff;
    gap: 10px;
}

.ledger-tag {
    display: flex;
    background-color: #F4F9FF;
}


.ledger-tag-text {
    color: #70bacc;
    width: 100px;
    padding: 10px;
}

.employee {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}


.employee-item {
    display: flex;
    align-items: center;
    gap: 20px;
}

.employee-item-number {
    width: 30px;
    height: 30px;
    background-color: #F97039;
    text-align: center;
    line-height: 30px;
    border-radius: 50%;
    color: #fff;
    font-style: oblique;
}

.avatar-name {
    width: 50px;
    height: 50px;
    background-color: #079BF8;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #fff;
}

.employee-nums {
    font-size: 20px;
    margin-left: auto;
    font-weight: bolder;
    color: #0D95FE;
}