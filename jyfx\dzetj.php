<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>到账额统计 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .card-header {
            background-color: #1389D3;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
        }
        .btn-primary {
            background-color: #1389D3;
            border-color: #1389D3;
        }
        .btn-primary:hover {
            background-color: rgba(19, 137, 211, 0.8);
            border-color: rgba(19, 137, 211, 0.8);
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">到账额统计</h2>

            <!-- 简单测试 -->
            <div class="alert alert-success" style="display:none;" id="test-alert">
                页面加载成功！
            </div>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <?php
                // 初始化所有变量
                $hj = 0;
                $hkbfb = 0;
                $wdzhj = 0;
                $ndhj = 0;

                // 查询本月到账额
                $sql="SELECT COALESCE(SUM(`ysje`), 0) as hj FROM `tuqoa_htsf` WHERE `sfjs`='是' and `sksj`>='$startDate' and `sksj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $hj=$row["hj"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月到账额</h5>
                            <h2 class="card-text">¥<?php echo $hj; ?>万</h2>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT SUM(COALESCE(yjje, 0)) AS 预计金额总和, SUM(COALESCE(ysje, 0)) AS 已收金额总和, CASE WHEN SUM(COALESCE(yjje, 0)) = 0 THEN 0 ELSE ROUND(SUM(COALESCE(ysje, 0)) / SUM(COALESCE(yjje, 0)) * 100, 2) END AS hkbfb FROM tuqoa_htsf where `sksj`>='$startDate' and `sksj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                $hkbfb = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $hkbfb=$row["hkbfb"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月回款率</h5>
                            <h2 class="card-text"><?php echo $hkbfb; ?>%</h2>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT COALESCE(SUM(`yjje`), 0) as hj FROM `tuqoa_htsf` WHERE `sfjs`<>'是' and `sksj`>='$startDate' and `sksj`<='$endDate' ";
                $result = mysqli_query($link, $sql);
                $wdzhj = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $wdzhj=$row["hj"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">待回款金额</h5>
                            <h2 class="card-text">¥<?php echo $wdzhj; ?>万</h2>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT SUM(ysje) AS ndhj FROM tuqoa_htsf WHERE YEAR(sksj) = YEAR(CURRENT_DATE) and `sfjs`='是'";
                $result = mysqli_query($link, $sql);
                $ndhj = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $ndhj=$row["ndhj"];
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">年度累计到账</h5>
                            <h2 class="card-text">¥<?php echo $ndhj; ?>万</h2>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            // 简化查询 - 生成最近6个月的数据
            $months = [];
            $yjjeData = [];
            $ysjeData = [];

            for ($i = 5; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $monthLabel = date('m', strtotime("-$i months"));
                $months[] = $monthLabel;

                // 查询该月预计金额
                $sql_yj = "SELECT COALESCE(SUM(yjje), 0) as yjhj FROM tuqoa_htsf WHERE DATE_FORMAT(sksj, '%Y-%m') = '$month'";
                $result_yj = mysqli_query($link, $sql_yj);
                $yj_row = $result_yj ? mysqli_fetch_assoc($result_yj) : ['yjhj' => 0];
                $yjjeData[] = (float)$yj_row['yjhj'];

                // 查询该月已收金额
                $sql_ys = "SELECT COALESCE(SUM(ysje), 0) as yshj FROM tuqoa_htsf WHERE DATE_FORMAT(sksj, '%Y-%m') = '$month' AND sfjs='是'";
                $result_ys = mysqli_query($link, $sql_ys);
                $ys_row = $result_ys ? mysqli_fetch_assoc($result_ys) : ['yshj' => 0];
                $ysjeData[] = (float)$ys_row['yshj'];
            }
            ?>
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">到账额趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">按项目分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">到账明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>预计金额</th>
                                            <th>到账金额</th>
                                            <th>差额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT * FROM `tuqoa_htsf` WHERE `sfjs`='是' and `sksj`>='$startDate' and `sksj`<='$endDate'";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $row["htmc"]; ?></td>
                                            <td>¥<?php echo $row["yjje"]; ?>万</td>
                                            <td>¥<?php echo $row["ysje"]; ?>万</td>
                                            <td>¥<?php echo ($row["yjje"]-$row["ysje"]); ?>万</td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='4'>查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    // 查询按项目分布的回款数据
    $sql = "SELECT htmc AS project_name, SUM(ysje) AS total_amount
            FROM tuqoa_htsf
            WHERE sfjs='是' AND sksj>='$startDate' AND sksj<='$endDate'
            GROUP BY htmc
            ORDER BY total_amount DESC
            LIMIT 10";
    $result = mysqli_query($link, $sql);
    $projectData = ['labels' => [], 'data' => []];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $projectData['labels'][] = $row["project_name"];
            $projectData['data'][] = (float)$row["total_amount"];
        }
    }
    ?>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            // 显示测试提示
            document.getElementById('test-alert').style.display = 'block';
            setTimeout(function() {
                document.getElementById('test-alert').style.display = 'none';
            }, 3000);

            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            // 到账额趋势图表
            const trendCtx = document.getElementById('paymentTrendChart').getContext('2d');
            const labels = <?php echo json_encode($months); ?>; // 自动转为 JS 数组格式
            new Chart(trendCtx, {
            type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '预计金额',
                        data: <?php echo json_encode($yjjeData); ?>,
                        borderColor: '#1389D3',
                        tension: 0.1
                    }, {
                        label: '已收金额',
                        data: <?php echo json_encode($ysjeData); ?>,
                        borderColor: '#28a745',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '回款率（%）'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });

            // 按项目分布图表
            const typeCtx = document.getElementById('paymentTypeChart').getContext('2d');
            const projectData = <?php echo json_encode($projectData); ?>;

            // 检查数据是否存在
            if (projectData && projectData.labels && projectData.labels.length > 0) {
                new Chart(typeCtx, {
                    type: 'pie',
                    data: {
                        labels: projectData.labels,
                        datasets: [{
                            data: projectData.data,
                            backgroundColor: [
                                '#1389D3', '#28a745', '#dc3545', '#ffc107', '#6f42c1',
                                '#fd7e14', '#20c997', '#6c757d', '#e83e8c', '#17a2b8'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'right',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        return `${label}: ¥${value}万 (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            } else {
                console.error('没有可用的项目数据');
                typeCtx.canvas.parentNode.innerHTML = '<p class="text-center text-muted">暂无项目数据</p>';
            }
        }
    </script>
</body>
</html> 